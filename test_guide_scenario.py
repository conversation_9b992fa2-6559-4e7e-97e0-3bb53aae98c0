#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
引导场景管理功能测试脚本
验证数据库和功能是否正常工作
"""

import sys
import os

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_database_connection():
    """测试数据库连接和表是否存在"""
    try:
        from app import create_app, db
        from sqlalchemy import text

        app = create_app()
        with app.app_context():
            # 测试数据库连接
            result = db.session.execute(text("SELECT COUNT(*) as count FROM guide_scenarios")).fetchone()
            scenario_count = result[0] if result else 0

            print(f"✅ 数据库连接成功")
            print(f"✅ guide_scenarios 表存在，包含 {scenario_count} 个场景配置")

            # 测试查询场景数据
            scenarios = db.session.execute(text("""
                SELECT scenario_type, name, theme_color, is_active
                FROM guide_scenarios
                WHERE is_active = 1
                ORDER BY scenario_type
            """)).fetchall()

            print(f"\n📋 活跃场景列表:")
            for scenario in scenarios:
                print(f"  - {scenario[0]}: {scenario[1]} ({scenario[2]})")

            return True

    except Exception as e:
        print(f"❌ 数据库测试失败: {str(e)}")
        return False

def test_scenario_service():
    """测试场景服务功能"""
    try:
        from app import create_app
        from app.services.scenario_guide_service import ScenarioGuideService

        app = create_app()
        with app.app_context():
            print(f"\n🔧 测试场景服务功能:")

            # 测试加载场景配置
            print("  - 测试从数据库加载场景配置...")
            success = ScenarioGuideService.load_scenarios_from_database()
            print(f"    {'✅ 成功' if success else '❌ 失败'}")

            # 测试场景检测
            print("  - 测试智能场景检测...")
            test_schools = [
                "北京市朝阳区实验小学",
                "清华大学",
                "山村希望小学"
            ]

            for school_name in test_schools:
                detected = ScenarioGuideService.detect_school_type(school_name)
                print(f"    {school_name} -> {detected}")

            # 测试个性化引导计划
            print("  - 测试个性化引导计划...")
            plan = ScenarioGuideService.create_personalized_guide_plan('primary')
            print(f"    小学场景引导计划: {len(plan['steps'])} 个步骤")

            # 测试场景配置获取
            print("  - 测试场景配置获取...")
            scenario_guide = ScenarioGuideService.get_scenario_guide('primary')
            if scenario_guide:
                print(f"    小学场景配置: {scenario_guide.get('welcome_message', '')[:30]}...")

            return True

    except Exception as e:
        print(f"❌ 场景服务测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_api_routes():
    """测试API路由"""
    try:
        from app import create_app

        app = create_app()
        with app.test_client() as client:
            print(f"\n🌐 测试API路由:")

            # 注意：这些测试需要登录，这里只测试路由是否存在
            print("  - 检查场景管理路由...")

            # 测试场景列表API（需要登录，这里只检查路由注册）
            try:
                with app.app_context():
                    from app.admin.guide_management_routes import guide_mgmt_bp
                    print(f"    ✅ 场景管理蓝图已注册: {guide_mgmt_bp.name}")
            except Exception as e:
                print(f"    ❌ 场景管理蓝图注册失败: {str(e)}")

            # 测试用户引导API
            try:
                with app.app_context():
                    from app.api.guide_routes import guide_api_bp
                    print(f"    ✅ 用户引导API蓝图已注册: {guide_api_bp.name}")
            except Exception as e:
                print(f"    ❌ 用户引导API蓝图注册失败: {str(e)}")

            return True

    except Exception as e:
        print(f"❌ API路由测试失败: {str(e)}")
        return False

def test_scenario_creation():
    """测试场景创建功能"""
    try:
        from app import create_app, db
        from app.services.scenario_guide_service import ScenarioGuideService

        app = create_app()
        with app.app_context():
            print(f"\n🆕 测试场景创建功能:")

            # 创建测试场景
            test_scenario = {
                'scenario_type': 'test_kindergarten',
                'name': '测试幼儿园',
                'description': '这是一个测试场景',
                'characteristics': ['测试特征1', '测试特征2'],
                'focus_areas': ['测试重点1', '测试重点2'],
                'welcome_message': '欢迎使用测试幼儿园场景！',
                'priority_steps': ['daily_management', 'suppliers'],
                'simplified_mode': True,
                'advanced_features': False
            }

            print("  - 创建测试场景...")
            success = ScenarioGuideService.create_new_scenario(test_scenario)
            print(f"    {'✅ 创建成功' if success else '❌ 创建失败'}")

            if success:
                # 验证场景是否已保存
                print("  - 验证场景是否已保存...")
                from sqlalchemy import text
                result = db.session.execute(text(
                    "SELECT name FROM guide_scenarios WHERE scenario_type = 'test_kindergarten'"
                )).fetchone()

                if result:
                    print(f"    ✅ 场景已保存: {result[0]}")

                    # 清理测试数据
                    print("  - 清理测试数据...")
                    db.session.execute(text(
                        "DELETE FROM guide_scenarios WHERE scenario_type = 'test_kindergarten'"
                    ))
                    db.session.commit()
                    print("    ✅ 测试数据已清理")
                else:
                    print("    ❌ 场景未找到")

            return success

    except Exception as e:
        print(f"❌ 场景创建测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("引导场景管理功能测试")
    print("=" * 60)

    tests = [
        ("数据库连接测试", test_database_connection),
        ("场景服务测试", test_scenario_service),
        ("API路由测试", test_api_routes),
        ("场景创建测试", test_scenario_creation)
    ]

    results = []

    for test_name, test_func in tests:
        print(f"\n🧪 {test_name}")
        print("-" * 40)
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}执行失败: {str(e)}")
            results.append((test_name, False))

    # 输出测试总结
    print("\n" + "=" * 60)
    print("测试结果总结")
    print("=" * 60)

    passed = 0
    total = len(results)

    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1

    print(f"\n总计: {passed}/{total} 个测试通过")

    if passed == total:
        print("\n🎉 所有测试通过！引导场景管理功能已就绪。")
        print("\n📝 下一步操作建议:")
        print("1. 访问 /admin/guide/scenarios 查看场景管理界面")
        print("2. 访问 /admin/guide/analytics 查看场景分析页面")
        print("3. 运行 python demo_guide_scenario.py 查看功能演示")
        print("4. 开始收集真实用户的使用数据")
    else:
        print(f"\n⚠️  有 {total - passed} 个测试失败，请检查相关配置。")

    return passed == total

if __name__ == '__main__':
    main()
