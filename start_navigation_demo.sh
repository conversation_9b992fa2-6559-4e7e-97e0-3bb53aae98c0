#!/bin/bash

echo ""
echo "========================================"
echo "   导航菜单优化演示启动器"
echo "========================================"
echo ""
echo "正在启动导航菜单优化演示..."
echo ""

# 检测操作系统并用相应的命令打开浏览器
if [[ "$OSTYPE" == "darwin"* ]]; then
    # macOS
    open "navigation_optimization_access.html"
elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
    # Linux
    xdg-open "navigation_optimization_access.html"
else
    echo "请手动打开 navigation_optimization_access.html 文件"
fi

echo "演示页面已在浏览器中打开！"
echo ""
echo "如果没有自动打开，请手动双击以下文件："
echo "navigation_optimization_access.html"
echo ""
echo "演示包含以下功能："
echo "- 导航菜单优化（从9+个减少到5个）"
echo "- 统一控制中心（主题切换、界面优化）"
echo "- 智能响应式设计"
echo "- 表格可读性修复"
echo ""
echo "快捷键：Ctrl + Shift + C 打开控制中心"
echo ""
read -p "按回车键退出..."
