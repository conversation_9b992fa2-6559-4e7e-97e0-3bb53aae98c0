<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>增强主题切换器演示</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="app/static/css/theme-colors.css">
    <link rel="stylesheet" href="app/static/css/enhanced-theme-switcher.css">
    <style>
        body {
            background-color: var(--theme-background, #ffffff);
            color: var(--theme-text, #111827);
            transition: all 0.3s ease;
        }
        
        .demo-section {
            margin: 2rem 0;
            padding: 2rem;
            border-radius: 12px;
            background-color: var(--theme-surface, #f9fafb);
            border: 1px solid var(--theme-border, #e5e7eb);
        }
        
        .theme-showcase {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1rem;
            margin: 2rem 0;
        }
        
        .theme-card {
            padding: 1.5rem;
            border-radius: 12px;
            background-color: var(--theme-surface, #ffffff);
            border: 2px solid var(--theme-border, #e5e7eb);
            transition: all 0.3s ease;
        }
        
        .theme-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
        }
        
        .feature-list li {
            padding: 0.5rem 0;
            border-bottom: 1px solid var(--theme-border, #e5e7eb);
        }
        
        .feature-list li:last-child {
            border-bottom: none;
        }
        
        .feature-list li i {
            color: var(--theme-primary, #2563eb);
            margin-right: 0.5rem;
        }
    </style>
</head>
<body data-theme="primary">
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-palette"></i>
                增强主题切换器演示
            </a>
            
            <!-- 主题切换器 -->
            <div class="navbar-nav ml-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link" href="#" id="themeDropdown" role="button" data-toggle="dropdown">
                        <i class="fas fa-palette"></i> 主题
                    </a>
                    <div class="dropdown-menu dropdown-menu-right" style="min-width: 320px; max-height: 500px; overflow-y: auto;">
                        <div class="dropdown-header d-flex align-items-center justify-content-between">
                            <span><i class="fas fa-palette"></i> 选择主题颜色</span>
                            <small class="text-muted">13 个主题</small>
                        </div>
                        
                        <!-- 专业商务系列 -->
                        <div class="theme-category-section">
                            <h6 class="dropdown-header">
                                <span class="category-icon">💼</span>
                                专业商务
                                <small class="text-muted">(2)</small>
                            </h6>
                            <a class="dropdown-item theme-option d-flex align-items-center active" href="#" data-theme="primary">
                                <div class="theme-preview-container me-3">
                                    <div class="theme-preview-box" style="background: #001F3F;">
                                        <div class="theme-accent-dot" style="background: #FF6B6B;"></div>
                                    </div>
                                </div>
                                <div class="theme-info flex-grow-1">
                                    <div class="theme-name">🌊 海洋蓝主题</div>
                                    <small class="theme-description text-muted">专业、信任、稳定</small>
                                </div>
                                <i class="fas fa-check text-success ms-2"></i>
                            </a>
                            <a class="dropdown-item theme-option d-flex align-items-center" href="#" data-theme="secondary">
                                <div class="theme-preview-container me-3">
                                    <div class="theme-preview-box" style="background: #475569;">
                                        <div class="theme-accent-dot" style="background: #F7FAFC;"></div>
                                    </div>
                                </div>
                                <div class="theme-info flex-grow-1">
                                    <div class="theme-name">🔘 现代灰主题</div>
                                    <small class="theme-description text-muted">简约、专业、平衡</small>
                                </div>
                            </a>
                        </div>
                        
                        <!-- 科技现代系列 -->
                        <div class="theme-category-section">
                            <h6 class="dropdown-header">
                                <span class="category-icon">🚀</span>
                                科技现代
                                <small class="text-muted">(2)</small>
                            </h6>
                            <a class="dropdown-item theme-option d-flex align-items-center" href="#" data-theme="modern">
                                <div class="theme-preview-container me-3">
                                    <div class="theme-preview-box" style="background: #1E1E2E;">
                                        <div class="theme-accent-dot" style="background: #00F5D4;"></div>
                                    </div>
                                </div>
                                <div class="theme-info flex-grow-1">
                                    <div class="theme-name">🚀 现代科技主题</div>
                                    <small class="theme-description text-muted">极简、未来感、科技</small>
                                </div>
                            </a>
                            <a class="dropdown-item theme-option d-flex align-items-center" href="#" data-theme="dark">
                                <div class="theme-preview-container me-3">
                                    <div class="theme-preview-box" style="background: #4F46E5;">
                                        <div class="theme-accent-dot" style="background: #10B981;"></div>
                                    </div>
                                </div>
                                <div class="theme-info flex-grow-1">
                                    <div class="theme-name">🌙 深色主题</div>
                                    <small class="theme-description text-muted">护眼、专业、现代</small>
                                </div>
                            </a>
                        </div>
                        
                        <!-- 自然健康系列 -->
                        <div class="theme-category-section">
                            <h6 class="dropdown-header">
                                <span class="category-icon">🌿</span>
                                自然健康
                                <small class="text-muted">(2)</small>
                            </h6>
                            <a class="dropdown-item theme-option d-flex align-items-center" href="#" data-theme="success">
                                <div class="theme-preview-container me-3">
                                    <div class="theme-preview-box" style="background: #4A7856;">
                                        <div class="theme-accent-dot" style="background: #BC8A5F;"></div>
                                    </div>
                                </div>
                                <div class="theme-info flex-grow-1">
                                    <div class="theme-name">🌿 自然绿主题</div>
                                    <small class="theme-description text-muted">健康、成长、和谐</small>
                                </div>
                            </a>
                            <a class="dropdown-item theme-option d-flex align-items-center" href="#" data-theme="nature">
                                <div class="theme-preview-container me-3">
                                    <div class="theme-preview-box" style="background: #22C55E;">
                                        <div class="theme-accent-dot" style="background: #F59E0B;"></div>
                                    </div>
                                </div>
                                <div class="theme-info flex-grow-1">
                                    <div class="theme-name">🍃 生态绿主题</div>
                                    <small class="theme-description text-muted">生态、环保、自然</small>
                                </div>
                            </a>
                        </div>
                        
                        <div class="dropdown-divider"></div>
                        <div class="dropdown-item-text">
                            <small class="text-muted">
                                <i class="fas fa-keyboard"></i> 快捷键: Ctrl+Shift+T
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="demo-section">
                    <h1 class="text-center mb-4">
                        <i class="fas fa-palette"></i>
                        增强主题切换器演示
                    </h1>
                    <p class="text-center lead">
                        基于色彩心理学和设计原理的专业主题颜色系统
                    </p>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="demo-section">
                    <h3><i class="fas fa-star"></i> 核心特性</h3>
                    <ul class="feature-list">
                        <li><i class="fas fa-check"></i> 13个专业主题，8个分类</li>
                        <li><i class="fas fa-check"></i> 基于色彩心理学设计</li>
                        <li><i class="fas fa-check"></i> 分类展示，便于选择</li>
                        <li><i class="fas fa-check"></i> 实时预览效果</li>
                        <li><i class="fas fa-check"></i> 智能推荐系统</li>
                        <li><i class="fas fa-check"></i> 使用统计追踪</li>
                        <li><i class="fas fa-check"></i> 收藏功能</li>
                        <li><i class="fas fa-check"></i> 配置导入导出</li>
                    </ul>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="demo-section">
                    <h3><i class="fas fa-keyboard"></i> 快捷操作</h3>
                    <ul class="feature-list">
                        <li><i class="fas fa-keyboard"></i> Ctrl+Shift+T 打开主题选择器</li>
                        <li><i class="fas fa-random"></i> 随机主题切换</li>
                        <li><i class="fas fa-heart"></i> 主题收藏管理</li>
                        <li><i class="fas fa-chart-bar"></i> 使用统计查看</li>
                        <li><i class="fas fa-download"></i> 配置导出</li>
                        <li><i class="fas fa-upload"></i> 配置导入</li>
                        <li><i class="fas fa-undo"></i> 重置为默认</li>
                        <li><i class="fas fa-eye"></i> 主题预览模式</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <div class="demo-section">
                    <h3 class="text-center mb-4"><i class="fas fa-palette"></i> 主题展示</h3>
                    <div class="theme-showcase">
                        <div class="theme-card">
                            <h5>🌊 海洋蓝主题</h5>
                            <p>专业、信任、稳定的商务风格，适合正式场合使用。</p>
                            <button class="btn btn-primary btn-sm" onclick="switchTheme('primary')">应用主题</button>
                        </div>
                        
                        <div class="theme-card">
                            <h5>🚀 现代科技主题</h5>
                            <p>极简、未来感的科技风格，适合创新型企业。</p>
                            <button class="btn btn-primary btn-sm" onclick="switchTheme('modern')">应用主题</button>
                        </div>
                        
                        <div class="theme-card">
                            <h5>🌿 自然绿主题</h5>
                            <p>健康、成长、和谐的自然风格，适合环保理念。</p>
                            <button class="btn btn-primary btn-sm" onclick="switchTheme('success')">应用主题</button>
                        </div>
                        
                        <div class="theme-card">
                            <h5>🌙 深色主题</h5>
                            <p>护眼、专业的深色模式，适合长时间使用。</p>
                            <button class="btn btn-primary btn-sm" onclick="switchTheme('dark')">应用主题</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="app/static/js/theme-switcher.js"></script>
    
    <script>
        // 简单的主题切换函数
        function switchTheme(theme) {
            if (window.themeSwitcher) {
                window.themeSwitcher.applyTheme(theme);
            } else {
                document.documentElement.setAttribute('data-theme', theme);
                document.body.setAttribute('data-theme', theme);
            }
        }
        
        // 演示快捷键
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.shiftKey && e.key === 'T') {
                e.preventDefault();
                $('#themeDropdown').dropdown('toggle');
            }
        });
        
        // 主题选项点击事件
        $(document).on('click', '.theme-option', function(e) {
            e.preventDefault();
            const theme = $(this).data('theme');
            switchTheme(theme);
            
            // 更新活跃状态
            $('.theme-option').removeClass('active').find('.fa-check').remove();
            $(this).addClass('active').append('<i class="fas fa-check text-success ms-2"></i>');
        });
    </script>
</body>
</html>
