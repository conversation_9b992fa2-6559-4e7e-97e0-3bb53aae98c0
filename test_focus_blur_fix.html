<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>焦点模糊效果修复测试</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="app/static/css/theme-colors.css">
    <link rel="stylesheet" href="app/static/css/disable-blur-effects.css">
    <style>
        body {
            background-color: var(--theme-surface, #f8f9fa);
            color: var(--theme-text, #333);
            padding: 2rem 0;
        }
        
        .test-section {
            margin: 2rem 0;
            padding: 2rem;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        
        .before-after-demo {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin: 2rem 0;
        }
        
        .demo-box {
            padding: 1.5rem;
            border-radius: 8px;
            border: 2px solid #e9ecef;
        }
        
        .before-box {
            background: #fff5f5;
            border-color: #fed7d7;
        }
        
        .after-box {
            background: #f0fff4;
            border-color: #c6f6d5;
        }
        
        /* 演示用的有问题的焦点效果 */
        .problematic-focus:focus {
            filter: blur(1px) !important;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5) !important;
            backdrop-filter: blur(5px) !important;
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin: 2rem 0;
        }
        
        .test-item {
            padding: 1rem;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            background: white;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-good { background-color: #10b981; }
        .status-bad { background-color: #ef4444; }
        .status-warning { background-color: #f59e0b; }
        
        @media (max-width: 768px) {
            .before-after-demo {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body data-theme="primary">
    <div class="container">
        <div class="test-section">
            <h1 class="text-center mb-4">
                <i class="fas fa-eye"></i>
                焦点模糊效果修复测试
            </h1>
            <p class="text-center lead">
                测试控件获得焦点时是否还有模糊效果，确保文字清晰可读
            </p>
        </div>

        <div class="test-section">
            <h3>🔍 问题说明</h3>
            <div class="alert alert-warning">
                <h6><i class="fas fa-exclamation-triangle"></i> 原问题</h6>
                <p class="mb-0">
                    当表单控件（输入框、按钮、链接等）获得焦点时，文字会变模糊，影响用户体验和可读性。
                    这通常是由CSS的 <code>:focus</code> 伪类中的 <code>filter: blur()</code>、<code>text-shadow</code> 或 <code>backdrop-filter</code> 属性造成的。
                </p>
            </div>
            
            <div class="alert alert-success">
                <h6><i class="fas fa-check-circle"></i> 解决方案</h6>
                <p class="mb-0">
                    我们已经在 <code>disable-blur-effects.css</code> 中添加了专门的焦点状态优化，
                    移除所有可能导致模糊的效果，同时保留清晰的焦点指示（边框高亮、下划线等）。
                </p>
            </div>
        </div>

        <div class="test-section">
            <h3>🧪 对比测试</h3>
            <div class="before-after-demo">
                <div class="demo-box before-box">
                    <h5>❌ 修复前（有问题）</h5>
                    <p>点击下面的控件，文字会变模糊：</p>
                    <input type="text" class="form-control problematic-focus mb-2" placeholder="点击我会模糊" value="这是测试文字">
                    <button class="btn btn-primary problematic-focus">模糊按钮</button>
                    <p class="mt-2"><small class="text-muted">这些控件使用了有问题的CSS</small></p>
                </div>
                
                <div class="demo-box after-box">
                    <h5>✅ 修复后（正常）</h5>
                    <p>点击下面的控件，文字保持清晰：</p>
                    <input type="text" class="form-control mb-2" placeholder="点击我保持清晰" value="这是测试文字">
                    <button class="btn btn-primary">清晰按钮</button>
                    <p class="mt-2"><small class="text-muted">这些控件使用了修复后的CSS</small></p>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>📋 全面测试</h3>
            <p>请逐一点击下面的控件，检查焦点状态下文字是否清晰：</p>
            
            <div class="test-grid">
                <div class="test-item">
                    <h6>📝 表单控件</h6>
                    <div class="form-group">
                        <label>文本输入框</label>
                        <input type="text" class="form-control" placeholder="请输入文字" value="测试文字内容">
                    </div>
                    <div class="form-group">
                        <label>密码输入框</label>
                        <input type="password" class="form-control" placeholder="请输入密码" value="password123">
                    </div>
                    <div class="form-group">
                        <label>文本域</label>
                        <textarea class="form-control" rows="3" placeholder="请输入多行文字">这是多行文字测试内容
可以检查焦点状态下的清晰度</textarea>
                    </div>
                    <div class="form-group">
                        <label>下拉选择</label>
                        <select class="form-control">
                            <option>选项一</option>
                            <option>选项二</option>
                            <option>选项三</option>
                        </select>
                    </div>
                </div>

                <div class="test-item">
                    <h6>🔘 按钮控件</h6>
                    <button class="btn btn-primary mb-2 mr-2">主要按钮</button>
                    <button class="btn btn-secondary mb-2 mr-2">次要按钮</button>
                    <button class="btn btn-success mb-2 mr-2">成功按钮</button>
                    <button class="btn btn-warning mb-2 mr-2">警告按钮</button>
                    <button class="btn btn-danger mb-2 mr-2">危险按钮</button>
                    <button class="btn btn-info mb-2 mr-2">信息按钮</button>
                    <button class="btn btn-light mb-2 mr-2">浅色按钮</button>
                    <button class="btn btn-dark mb-2 mr-2">深色按钮</button>
                </div>

                <div class="test-item">
                    <h6>🔗 链接控件</h6>
                    <p><a href="#" class="text-primary">普通链接文字</a></p>
                    <p><a href="#" class="text-success">成功链接文字</a></p>
                    <p><a href="#" class="text-warning">警告链接文字</a></p>
                    <p><a href="#" class="text-danger">危险链接文字</a></p>
                    <p><a href="#" class="btn btn-link">按钮样式链接</a></p>
                </div>

                <div class="test-item">
                    <h6>☑️ 选择控件</h6>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="check1">
                        <label class="form-check-label" for="check1">
                            复选框选项一
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="check2">
                        <label class="form-check-label" for="check2">
                            复选框选项二
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="radio" id="radio1">
                        <label class="form-check-label" for="radio1">
                            单选框选项一
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="radio" id="radio2">
                        <label class="form-check-label" for="radio2">
                            单选框选项二
                        </label>
                    </div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>📊 测试结果</h3>
            <div class="alert alert-info">
                <h6><i class="fas fa-info-circle"></i> 检查清单</h6>
                <div class="row">
                    <div class="col-md-6">
                        <p><span class="status-indicator status-good"></span>文字保持清晰</p>
                        <p><span class="status-indicator status-good"></span>无模糊效果</p>
                        <p><span class="status-indicator status-good"></span>无过度阴影</p>
                        <p><span class="status-indicator status-good"></span>边框高亮正常</p>
                    </div>
                    <div class="col-md-6">
                        <p><span class="status-indicator status-good"></span>焦点指示清晰</p>
                        <p><span class="status-indicator status-good"></span>键盘导航正常</p>
                        <p><span class="status-indicator status-good"></span>无性能问题</p>
                        <p><span class="status-indicator status-good"></span>响应式适配</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>⚙️ 控制选项</h3>
            <div class="row">
                <div class="col-md-6">
                    <button class="btn btn-success btn-block" onclick="testFocusClarity()">
                        <i class="fas fa-check"></i> 测试焦点清晰度
                    </button>
                </div>
                <div class="col-md-6">
                    <button class="btn btn-info btn-block" onclick="toggleBlurControl()">
                        <i class="fas fa-cog"></i> 打开模糊控制面板
                    </button>
                </div>
            </div>
            <div class="mt-3">
                <small class="text-muted">
                    <i class="fas fa-keyboard"></i> 
                    快捷键：Tab 键导航测试，Ctrl+Shift+B 打开控制面板
                </small>
            </div>
        </div>

        <div class="test-section">
            <h3>💡 技术说明</h3>
            <div class="row">
                <div class="col-md-6">
                    <h6>移除的效果：</h6>
                    <ul>
                        <li><code>filter: blur()</code></li>
                        <li><code>backdrop-filter: blur()</code></li>
                        <li><code>text-shadow</code> (过度)</li>
                        <li>其他模糊相关属性</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6>保留的效果：</h6>
                    <ul>
                        <li>清晰的边框高亮</li>
                        <li>适度的 <code>box-shadow</code></li>
                        <li>文字下划线</li>
                        <li>轮廓线指示</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="app/static/js/blur-control.js"></script>
    
    <script>
        function testFocusClarity() {
            alert('请使用 Tab 键在页面中导航，观察每个控件获得焦点时文字是否清晰。\n\n如果文字模糊，请检查 CSS 设置或联系开发人员。');
        }

        function toggleBlurControl() {
            const panel = document.getElementById('blur-control-panel');
            if (panel) {
                panel.style.display = panel.style.display === 'none' ? 'block' : 'none';
            } else {
                alert('模糊控制面板正在加载中，请稍后再试。');
            }
        }

        // 页面加载完成后的测试
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🧪 焦点模糊效果修复测试页面已加载');
            
            // 自动测试焦点效果
            setTimeout(() => {
                const firstInput = document.querySelector('input[type="text"]');
                if (firstInput) {
                    firstInput.focus();
                    console.log('✅ 自动聚焦到第一个输入框，请检查文字是否清晰');
                }
            }, 1000);
        });

        // 键盘导航测试
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Tab') {
                console.log('🔍 Tab 导航测试：', document.activeElement);
            }
        });
    </script>
</body>
</html>
