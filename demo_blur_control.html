<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模糊效果控制演示</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="app/static/css/theme-colors.css">
    <link rel="stylesheet" href="app/static/css/disable-blur-effects.css">
    <style>
        body {
            background-color: var(--theme-surface, #f8f9fa);
            color: var(--theme-text, #333);
        }
        
        .demo-section {
            margin: 2rem 0;
            padding: 2rem;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        
        .blur-example {
            padding: 2rem;
            margin: 1rem 0;
            border-radius: 8px;
            border: 2px solid #e9ecef;
            position: relative;
        }
        
        /* 演示用的模糊效果 */
        .example-backdrop-blur {
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            background: rgba(255,255,255,0.8);
        }
        
        .example-text-shadow {
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }
        
        .example-image-blur {
            filter: blur(2px);
        }
        
        .example-box-shadow {
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        }
        
        .control-panel {
            position: sticky;
            top: 20px;
            background: white;
            padding: 1.5rem;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        
        .before-after {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin: 2rem 0;
        }
        
        .before-after > div {
            padding: 1.5rem;
            border-radius: 8px;
            border: 2px solid #e9ecef;
        }
        
        .before {
            background: #f8f9fa;
        }
        
        .after {
            background: #e8f5e8;
        }
        
        @media (max-width: 768px) {
            .before-after {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body data-theme="primary">
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-eye"></i>
                模糊效果控制演示
            </a>
            <div class="navbar-nav ml-auto">
                <span class="navbar-text text-white">
                    智能模糊效果管理系统
                </span>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-lg-8">
                <div class="demo-section">
                    <h1 class="mb-4">
                        <i class="fas fa-eye"></i>
                        模糊效果控制系统演示
                    </h1>
                    <p class="lead">
                        这个系统允许用户根据个人偏好和设备性能，智能控制界面中的各种模糊效果。
                    </p>
                </div>

                <div class="demo-section">
                    <h3>🎯 处理策略说明</h3>
                    <div class="before-after">
                        <div class="before">
                            <h5>❌ 移除的模糊效果</h5>
                            <ul>
                                <li><strong>背景模糊</strong>：backdrop-filter: blur()</li>
                                <li><strong>装饰性图片模糊</strong>：filter: blur()</li>
                                <li><strong>过度文字阴影</strong>：text-shadow</li>
                                <li><strong>玻璃效果</strong>：毛玻璃背景</li>
                            </ul>
                        </div>
                        <div class="after">
                            <h5>✅ 保留的有用效果</h5>
                            <ul>
                                <li><strong>轻微阴影</strong>：提供层次感</li>
                                <li><strong>焦点效果</strong>：表单验证提示</li>
                                <li><strong>加载状态</strong>：数据加载占位符</li>
                                <li><strong>按钮阴影</strong>：交互反馈</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="demo-section">
                    <h3>🔧 效果对比演示</h3>
                    
                    <h5>1. 背景模糊效果</h5>
                    <div class="blur-example example-backdrop-blur">
                        <h6>毛玻璃效果示例</h6>
                        <p>这个区域使用了 backdrop-filter: blur(10px) 效果。在启用模糊控制后，这种效果会被移除，保持界面清晰。</p>
                    </div>

                    <h5>2. 文字阴影效果</h5>
                    <div class="blur-example">
                        <h6 class="example-text-shadow">带阴影的标题</h6>
                        <p>上面的标题使用了 text-shadow 效果。过度的文字阴影会影响可读性，特别是在小屏幕设备上。</p>
                    </div>

                    <h5>3. 盒子阴影效果</h5>
                    <div class="blur-example example-box-shadow">
                        <h6>深度阴影卡片</h6>
                        <p>这个卡片使用了较强的 box-shadow 效果。系统会保留轻微的阴影来维持层次感，但移除过度的模糊。</p>
                    </div>
                </div>

                <div class="demo-section">
                    <h3>📱 响应式优化</h3>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6>桌面端</h6>
                                </div>
                                <div class="card-body">
                                    <ul>
                                        <li>保留轻微阴影效果</li>
                                        <li>移除背景模糊</li>
                                        <li>优化文字渲染</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6>移动端</h6>
                                </div>
                                <div class="card-body">
                                    <ul>
                                        <li>完全移除模糊效果</li>
                                        <li>提高触控响应</li>
                                        <li>节省电池消耗</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="demo-section">
                    <h3>⚡ 性能优势</h3>
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle"></i> 性能提升</h6>
                        <ul class="mb-0">
                            <li><strong>GPU 负载降低</strong>：减少图形处理器压力</li>
                            <li><strong>电池续航提升</strong>：特别是移动设备</li>
                            <li><strong>渲染速度加快</strong>：页面响应更流畅</li>
                            <li><strong>兼容性增强</strong>：支持更多老旧设备</li>
                        </ul>
                    </div>
                </div>

                <div class="demo-section">
                    <h3>🎮 快捷操作</h3>
                    <div class="row">
                        <div class="col-md-6">
                            <button class="btn btn-danger btn-block" onclick="disableAllBlur()">
                                <i class="fas fa-ban"></i> 禁用所有模糊效果
                            </button>
                        </div>
                        <div class="col-md-6">
                            <button class="btn btn-success btn-block" onclick="enableAllEffects()">
                                <i class="fas fa-magic"></i> 启用所有视觉效果
                            </button>
                        </div>
                    </div>
                    <div class="mt-3">
                        <small class="text-muted">
                            <i class="fas fa-keyboard"></i> 
                            快捷键：Ctrl+Shift+B 打开/关闭控制面板
                        </small>
                    </div>
                </div>
            </div>

            <div class="col-lg-4">
                <div class="control-panel">
                    <h5><i class="fas fa-cog"></i> 手动控制</h5>
                    <p class="text-muted">页面右上角会自动显示控制面板，您也可以使用以下按钮进行快速操作：</p>
                    
                    <div class="form-group">
                        <button class="btn btn-outline-primary btn-sm btn-block" onclick="toggleBlurPanel()">
                            <i class="fas fa-eye"></i> 显示/隐藏控制面板
                        </button>
                    </div>
                    
                    <div class="form-group">
                        <button class="btn btn-outline-secondary btn-sm btn-block" onclick="resetToDefault()">
                            <i class="fas fa-undo"></i> 重置为推荐设置
                        </button>
                    </div>

                    <hr>
                    
                    <h6>📊 当前设置状态</h6>
                    <div id="current-settings">
                        <small class="text-muted">加载中...</small>
                    </div>
                </div>

                <div class="demo-section">
                    <h6>💡 使用建议</h6>
                    <div class="alert alert-warning">
                        <small>
                            <strong>推荐设置：</strong><br>
                            • 禁用背景模糊 ✓<br>
                            • 禁用文字阴影 ✓<br>
                            • 保留盒子阴影 ✓<br>
                            • 保留焦点效果 ✓
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="app/static/js/blur-control.js"></script>
    
    <script>
        // 演示页面的控制函数
        function disableAllBlur() {
            if (window.blurController) {
                window.blurController.disableAllBlur();
                updateCurrentSettings();
                showNotification('已禁用所有模糊效果', 'success');
            }
        }

        function enableAllEffects() {
            if (window.blurController) {
                window.blurController.enableAllEffects();
                updateCurrentSettings();
                showNotification('已启用所有视觉效果', 'info');
            }
        }

        function toggleBlurPanel() {
            const panel = document.getElementById('blur-control-panel');
            if (panel) {
                panel.style.display = panel.style.display === 'none' ? 'block' : 'none';
            }
        }

        function resetToDefault() {
            if (window.blurController) {
                window.blurController.resetSettings();
                window.blurController.applySettings();
                window.blurController.saveSettings();
                window.blurController.updateFormFromSettings();
                updateCurrentSettings();
                showNotification('已重置为推荐设置', 'info');
            }
        }

        function updateCurrentSettings() {
            if (window.blurController) {
                const settings = window.blurController.settings;
                const html = `
                    <small>
                        背景模糊: ${settings.disableBackdropBlur ? '❌ 已禁用' : '✅ 已启用'}<br>
                        文字阴影: ${settings.disableTextShadow ? '❌ 已禁用' : '✅ 已启用'}<br>
                        图片模糊: ${settings.disableImageBlur ? '❌ 已禁用' : '✅ 已启用'}<br>
                        盒子阴影: ${settings.keepBoxShadow ? '✅ 已保留' : '❌ 已移除'}<br>
                        焦点效果: ${settings.keepFocusEffects ? '✅ 已保留' : '❌ 已移除'}<br>
                        减少动画: ${settings.reducedMotion ? '✅ 已启用' : '❌ 已禁用'}
                    </small>
                `;
                document.getElementById('current-settings').innerHTML = html;
            }
        }

        function showNotification(message, type) {
            const notification = document.createElement('div');
            notification.className = `alert alert-${type} alert-dismissible fade show`;
            notification.style.cssText = `
                position: fixed;
                top: 80px;
                left: 50%;
                transform: translateX(-50%);
                z-index: 10000;
                min-width: 300px;
                text-align: center;
            `;
            notification.innerHTML = `
                ${message}
                <button type="button" class="close" data-dismiss="alert">
                    <span>&times;</span>
                </button>
            `;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 3000);
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 等待模糊控制器初始化
            setTimeout(() => {
                updateCurrentSettings();
            }, 1000);
            
            console.log('🎨 模糊效果控制演示页面已加载');
        });
    </script>
</body>
</html>
