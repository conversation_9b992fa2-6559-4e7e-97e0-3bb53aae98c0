/* 全面界面控制系统 - 表格、字体、间距、背景等 */

/* === CSS 变量定义 === */
:root {
    /* 字体大小控制 */
    --font-size-xs: 0.75rem;      /* 12px */
    --font-size-sm: 0.875rem;     /* 14px */
    --font-size-base: 1rem;       /* 16px */
    --font-size-lg: 1.125rem;     /* 18px */
    --font-size-xl: 1.25rem;      /* 20px */
    --font-size-2xl: 1.5rem;      /* 24px */
    
    /* 间距控制 */
    --spacing-xs: 0.25rem;        /* 4px */
    --spacing-sm: 0.5rem;         /* 8px */
    --spacing-base: 1rem;         /* 16px */
    --spacing-lg: 1.5rem;         /* 24px */
    --spacing-xl: 2rem;           /* 32px */
    --spacing-2xl: 3rem;          /* 48px */
    
    /* 表格控制 */
    --table-cell-padding: 0.75rem;
    --table-header-height: 3rem;
    --table-row-height: 2.5rem;
    --table-border-width: 1px;
    --table-border-radius: 0.375rem;
    
    /* 背景控制 */
    --bg-opacity: 1;
    --bg-blur: 0px;
    --bg-brightness: 1;
    --bg-contrast: 1;
    
    /* 用户偏好设置 */
    --ui-density: normal;         /* compact, normal, spacious */
    --font-scale: 1;              /* 0.8, 1, 1.2, 1.4 */
    --table-style: modern;        /* classic, modern, minimal */
    --background-style: clean;    /* clean, textured, gradient */
}

/* === 字体大小控制系统 === */

/* 全局字体缩放 */
.font-scale-xs { --font-scale: 0.8; }
.font-scale-sm { --font-scale: 0.9; }
.font-scale-base { --font-scale: 1; }
.font-scale-lg { --font-scale: 1.1; }
.font-scale-xl { --font-scale: 1.2; }
.font-scale-2xl { --font-scale: 1.4; }

/* 应用字体缩放 */
.font-scale-xs *,
.font-scale-sm *,
.font-scale-base *,
.font-scale-lg *,
.font-scale-xl *,
.font-scale-2xl * {
    font-size: calc(var(--font-size-base) * var(--font-scale)) !important;
}

/* 特定元素字体大小 */
.text-xs { font-size: var(--font-size-xs) !important; }
.text-sm { font-size: var(--font-size-sm) !important; }
.text-base { font-size: var(--font-size-base) !important; }
.text-lg { font-size: var(--font-size-lg) !important; }
.text-xl { font-size: var(--font-size-xl) !important; }
.text-2xl { font-size: var(--font-size-2xl) !important; }

/* 标题字体大小 */
h1, .h1 { font-size: calc(var(--font-size-2xl) * var(--font-scale)) !important; }
h2, .h2 { font-size: calc(var(--font-size-xl) * var(--font-scale)) !important; }
h3, .h3 { font-size: calc(var(--font-size-lg) * var(--font-scale)) !important; }
h4, .h4 { font-size: calc(var(--font-size-base) * var(--font-scale)) !important; }
h5, .h5 { font-size: calc(var(--font-size-sm) * var(--font-scale)) !important; }
h6, .h6 { font-size: calc(var(--font-size-xs) * var(--font-scale)) !important; }

/* === 间距控制系统 === */

/* UI 密度控制 */
.ui-density-compact {
    --spacing-base: 0.5rem;
    --spacing-lg: 1rem;
    --spacing-xl: 1.5rem;
    --table-cell-padding: 0.5rem;
    --table-row-height: 2rem;
}

.ui-density-normal {
    --spacing-base: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --table-cell-padding: 0.75rem;
    --table-row-height: 2.5rem;
}

.ui-density-spacious {
    --spacing-base: 1.5rem;
    --spacing-lg: 2rem;
    --spacing-xl: 3rem;
    --table-cell-padding: 1rem;
    --table-row-height: 3rem;
}

/* 间距工具类 */
.p-xs { padding: var(--spacing-xs) !important; }
.p-sm { padding: var(--spacing-sm) !important; }
.p-base { padding: var(--spacing-base) !important; }
.p-lg { padding: var(--spacing-lg) !important; }
.p-xl { padding: var(--spacing-xl) !important; }

.m-xs { margin: var(--spacing-xs) !important; }
.m-sm { margin: var(--spacing-sm) !important; }
.m-base { margin: var(--spacing-base) !important; }
.m-lg { margin: var(--spacing-lg) !important; }
.m-xl { margin: var(--spacing-xl) !important; }

/* === 表格控制系统 === */

/* 现代表格样式 */
.table-style-modern {
    --table-border-radius: 0.5rem;
    --table-border-width: 0;
    --table-cell-padding: 1rem;
}

.table-style-modern .table {
    border-radius: var(--table-border-radius);
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    border: none;
}

.table-style-modern .table thead th {
    background: linear-gradient(135deg, var(--theme-primary) 0%, var(--theme-primary-light) 100%);
    color: white;
    font-weight: 600;
    text-transform: uppercase;
    font-size: var(--font-size-sm);
    letter-spacing: 0.5px;
    padding: var(--table-cell-padding);
    border: none;
    height: var(--table-header-height);
    vertical-align: middle;
}

.table-style-modern .table tbody tr {
    transition: all 0.2s ease;
    height: var(--table-row-height);
}

.table-style-modern .table tbody tr:hover {
    background-color: var(--theme-primary-50, rgba(59, 130, 246, 0.05));
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.table-style-modern .table tbody td {
    padding: var(--table-cell-padding);
    border: none;
    border-bottom: 1px solid var(--theme-border, #e5e7eb);
    vertical-align: middle;
    font-size: calc(var(--font-size-sm) * var(--font-scale));
}

/* 经典表格样式 */
.table-style-classic .table {
    border: var(--table-border-width) solid var(--theme-border, #dee2e6);
    border-radius: var(--table-border-radius);
}

.table-style-classic .table thead th {
    background-color: var(--theme-surface-dark, #f8f9fa);
    color: var(--theme-text, #495057);
    font-weight: 600;
    padding: var(--table-cell-padding);
    border-bottom: 2px solid var(--theme-border, #dee2e6);
    height: var(--table-header-height);
}

.table-style-classic .table tbody td {
    padding: var(--table-cell-padding);
    border-bottom: var(--table-border-width) solid var(--theme-border, #dee2e6);
    height: var(--table-row-height);
}

/* 极简表格样式 */
.table-style-minimal .table {
    border: none;
    background: transparent;
}

.table-style-minimal .table thead th {
    background: transparent;
    color: var(--theme-text-secondary, #6b7280);
    font-weight: 500;
    padding: calc(var(--table-cell-padding) * 0.75);
    border: none;
    border-bottom: 1px solid var(--theme-border, #e5e7eb);
    height: calc(var(--table-header-height) * 0.8);
    font-size: var(--font-size-xs);
    text-transform: uppercase;
    letter-spacing: 1px;
}

.table-style-minimal .table tbody td {
    padding: calc(var(--table-cell-padding) * 0.75);
    border: none;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    height: calc(var(--table-row-height) * 0.9);
}

.table-style-minimal .table tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.02);
}

/* 表格密度控制 */
.table-density-compact .table thead th,
.table-density-compact .table tbody td {
    padding: calc(var(--table-cell-padding) * 0.6) !important;
    font-size: calc(var(--font-size-xs) * var(--font-scale)) !important;
}

.table-density-normal .table thead th,
.table-density-normal .table tbody td {
    padding: var(--table-cell-padding) !important;
    font-size: calc(var(--font-size-sm) * var(--font-scale)) !important;
}

.table-density-spacious .table thead th,
.table-density-spacious .table tbody td {
    padding: calc(var(--table-cell-padding) * 1.4) !important;
    font-size: calc(var(--font-size-base) * var(--font-scale)) !important;
}

/* 表格响应式 */
.table-responsive {
    border-radius: var(--table-border-radius);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* 表格排序指示器 */
.table th.sortable {
    cursor: pointer;
    user-select: none;
    position: relative;
}

.table th.sortable:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.table th.sortable::after {
    content: '↕';
    position: absolute;
    right: 8px;
    opacity: 0.5;
    font-size: 0.8em;
}

.table th.sortable.asc::after {
    content: '↑';
    opacity: 1;
}

.table th.sortable.desc::after {
    content: '↓';
    opacity: 1;
}

/* === 背景控制系统 === */

/* 清洁背景 */
.bg-style-clean {
    background: var(--theme-surface, #ffffff);
    background-image: none;
}

/* 纹理背景 */
.bg-style-textured {
    background: var(--theme-surface, #ffffff);
    background-image: 
        radial-gradient(circle at 1px 1px, rgba(0,0,0,0.02) 1px, transparent 0);
    background-size: 20px 20px;
}

/* 渐变背景 */
.bg-style-gradient {
    background: linear-gradient(135deg, 
        var(--theme-surface, #ffffff) 0%, 
        var(--theme-surface-dark, #f8f9fa) 100%);
}

/* 背景透明度控制 */
.bg-opacity-10 { background-color: rgba(var(--theme-surface-rgb, 255, 255, 255), 0.1) !important; }
.bg-opacity-25 { background-color: rgba(var(--theme-surface-rgb, 255, 255, 255), 0.25) !important; }
.bg-opacity-50 { background-color: rgba(var(--theme-surface-rgb, 255, 255, 255), 0.5) !important; }
.bg-opacity-75 { background-color: rgba(var(--theme-surface-rgb, 255, 255, 255), 0.75) !important; }
.bg-opacity-90 { background-color: rgba(var(--theme-surface-rgb, 255, 255, 255), 0.9) !important; }

/* === 卡片和容器控制 === */

/* 卡片样式变体 */
.card-style-flat {
    box-shadow: none !important;
    border: 1px solid var(--theme-border, #e5e7eb);
}

.card-style-elevated {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05), 0 10px 15px rgba(0, 0, 0, 0.1) !important;
    border: none;
}

.card-style-outlined {
    box-shadow: none !important;
    border: 2px solid var(--theme-primary, #3b82f6);
}

/* 卡片间距控制 */
.card-padding-sm .card-body { padding: var(--spacing-sm) !important; }
.card-padding-base .card-body { padding: var(--spacing-base) !important; }
.card-padding-lg .card-body { padding: var(--spacing-lg) !important; }
.card-padding-xl .card-body { padding: var(--spacing-xl) !important; }

/* === 按钮控制系统 === */

/* 按钮大小控制 */
.btn-xs {
    padding: 0.25rem 0.5rem;
    font-size: var(--font-size-xs);
    border-radius: 0.25rem;
}

.btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: var(--font-size-sm);
    border-radius: 0.375rem;
}

.btn-lg {
    padding: 0.75rem 1.5rem;
    font-size: var(--font-size-lg);
    border-radius: 0.5rem;
}

.btn-xl {
    padding: 1rem 2rem;
    font-size: var(--font-size-xl);
    border-radius: 0.75rem;
}

/* 按钮样式变体 */
.btn-style-flat {
    box-shadow: none !important;
    border-width: 1px;
}

.btn-style-elevated {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
    border: none;
}

.btn-style-outlined {
    background: transparent !important;
    border-width: 2px;
    color: var(--theme-primary, #3b82f6) !important;
}

/* === 表单控件控制 === */

/* 表单控件大小 */
.form-control-xs {
    padding: 0.25rem 0.5rem;
    font-size: var(--font-size-xs);
    height: calc(1.5em + 0.5rem + 2px);
}

.form-control-sm {
    padding: 0.375rem 0.75rem;
    font-size: var(--font-size-sm);
    height: calc(1.5em + 0.75rem + 2px);
}

.form-control-lg {
    padding: 0.75rem 1rem;
    font-size: var(--font-size-lg);
    height: calc(1.5em + 1.5rem + 2px);
}

.form-control-xl {
    padding: 1rem 1.25rem;
    font-size: var(--font-size-xl);
    height: calc(1.5em + 2rem + 2px);
}

/* === 响应式控制 === */

/* 移动端优化 */
@media (max-width: 768px) {
    :root {
        --font-scale: 0.9;
        --spacing-base: 0.75rem;
        --table-cell-padding: 0.5rem;
        --table-row-height: 2.25rem;
    }
    
    .table-responsive {
        font-size: var(--font-size-xs);
    }
    
    .card-body {
        padding: var(--spacing-sm) !important;
    }
}

/* 大屏幕优化 */
@media (min-width: 1200px) {
    :root {
        --font-scale: 1.1;
        --spacing-base: 1.25rem;
        --table-cell-padding: 1rem;
        --table-row-height: 3rem;
    }
}
