/**
 * 高级主题切换器 - 支持多种专业主题
 * 基于您提供的专业主题颜色系统
 */

class AdvancedThemeSwitcher {
    constructor() {
        this.themes = {
            'primary': {
                name: '海洋蓝',
                description: '专业、信任、稳定',
                icon: '🌊',
                category: 'professional'
            },
            'modern': {
                name: '现代科技',
                description: '极简、未来感',
                icon: '🚀',
                category: 'tech'
            },
            'nature': {
                name: '自然绿',
                description: '健康、成长',
                icon: '🌿',
                category: 'natural'
            },
            'energy': {
                name: '活力橙',
                description: '创新、温暖',
                icon: '🔥',
                category: 'vibrant'
            },
            'elegant': {
                name: '优雅紫',
                description: '创新、神秘',
                icon: '💜',
                category: 'elegant'
            },
            'danger': {
                name: '深邃红',
                description: '警示、重要',
                icon: '🔴',
                category: 'alert'
            },
            'dark': {
                name: '深色模式',
                description: '护眼、专业',
                icon: '🌙',
                category: 'dark'
            },
            'classic-neutral': {
                name: '经典中性',
                description: '稳重、传统',
                icon: '🏛️',
                category: 'neutral'
            },
            'modern-neutral': {
                name: '现代中性',
                description: '简约、平衡',
                icon: '⚪',
                category: 'neutral'
            }
        };

        this.currentTheme = this.loadTheme();
        this.init();
    }

    init() {
        this.applyTheme(this.currentTheme);
        this.createThemeSwitcher();
        this.bindEvents();
        this.detectSystemPreference();
    }

    /**
     * 创建主题切换器界面
     */
    createThemeSwitcher() {
        // 检查是否已存在主题切换器
        if (document.getElementById('theme-switcher-container')) {
            return;
        }

        const container = document.createElement('div');
        container.id = 'theme-switcher-container';
        container.innerHTML = `
            <div class="theme-switcher-trigger" id="theme-switcher-trigger">
                <i class="fas fa-palette"></i>
                <span>主题</span>
            </div>
            <div class="theme-switcher-panel" id="theme-switcher-panel">
                <div class="theme-switcher-header">
                    <h4><i class="fas fa-palette"></i> 选择主题</h4>
                    <button class="theme-switcher-close" id="theme-switcher-close">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="theme-switcher-content">
                    ${this.renderThemeCategories()}
                </div>
                <div class="theme-switcher-footer">
                    <small class="text-muted">
                        <i class="fas fa-info-circle"></i>
                        主题设置会自动保存
                    </small>
                </div>
            </div>
        `;

        // 添加样式
        this.addThemeSwitcherStyles();
        
        // 添加到页面
        document.body.appendChild(container);
    }

    /**
     * 渲染主题分类
     */
    renderThemeCategories() {
        const categories = {
            'professional': '专业商务',
            'tech': '科技现代', 
            'natural': '自然健康',
            'vibrant': '活力创新',
            'elegant': '优雅精致',
            'alert': '警示提醒',
            'dark': '深色护眼',
            'neutral': '中性平衡'
        };

        let html = '';
        
        Object.entries(categories).forEach(([category, categoryName]) => {
            const categoryThemes = Object.entries(this.themes)
                .filter(([_, theme]) => theme.category === category);
            
            if (categoryThemes.length > 0) {
                html += `
                    <div class="theme-category">
                        <h5 class="theme-category-title">${categoryName}</h5>
                        <div class="theme-options">
                            ${categoryThemes.map(([key, theme]) => `
                                <div class="theme-option ${key === this.currentTheme ? 'active' : ''}" 
                                     data-theme="${key}">
                                    <div class="theme-preview" data-theme="${key}">
                                        <div class="theme-color-primary"></div>
                                        <div class="theme-color-accent"></div>
                                        <div class="theme-color-surface"></div>
                                    </div>
                                    <div class="theme-info">
                                        <div class="theme-name">
                                            <span class="theme-icon">${theme.icon}</span>
                                            ${theme.name}
                                        </div>
                                        <div class="theme-description">${theme.description}</div>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                `;
            }
        });

        return html;
    }

    /**
     * 添加主题切换器样式
     */
    addThemeSwitcherStyles() {
        if (document.getElementById('theme-switcher-styles')) {
            return;
        }

        const styles = document.createElement('style');
        styles.id = 'theme-switcher-styles';
        styles.textContent = `
            #theme-switcher-container {
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 9999;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            }

            .theme-switcher-trigger {
                background: var(--theme-primary, #2563eb);
                color: white;
                padding: 12px 16px;
                border-radius: 25px;
                cursor: pointer;
                display: flex;
                align-items: center;
                gap: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                transition: all 0.3s ease;
                font-size: 14px;
                font-weight: 500;
            }

            .theme-switcher-trigger:hover {
                transform: translateY(-2px);
                box-shadow: 0 6px 20px rgba(0,0,0,0.2);
            }

            .theme-switcher-panel {
                position: absolute;
                top: 60px;
                right: 0;
                width: 400px;
                max-height: 600px;
                background: white;
                border-radius: 12px;
                box-shadow: 0 10px 40px rgba(0,0,0,0.15);
                display: none;
                overflow: hidden;
                border: 1px solid #e5e7eb;
            }

            .theme-switcher-panel.show {
                display: block;
                animation: slideInDown 0.3s ease;
            }

            @keyframes slideInDown {
                from {
                    opacity: 0;
                    transform: translateY(-20px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }

            .theme-switcher-header {
                padding: 16px 20px;
                border-bottom: 1px solid #e5e7eb;
                display: flex;
                justify-content: space-between;
                align-items: center;
                background: #f9fafb;
            }

            .theme-switcher-header h4 {
                margin: 0;
                font-size: 16px;
                font-weight: 600;
                color: #374151;
                display: flex;
                align-items: center;
                gap: 8px;
            }

            .theme-switcher-close {
                background: none;
                border: none;
                color: #6b7280;
                cursor: pointer;
                padding: 4px;
                border-radius: 4px;
                transition: all 0.2s ease;
            }

            .theme-switcher-close:hover {
                background: #e5e7eb;
                color: #374151;
            }

            .theme-switcher-content {
                max-height: 480px;
                overflow-y: auto;
                padding: 16px 20px;
            }

            .theme-category {
                margin-bottom: 24px;
            }

            .theme-category:last-child {
                margin-bottom: 0;
            }

            .theme-category-title {
                font-size: 14px;
                font-weight: 600;
                color: #374151;
                margin: 0 0 12px 0;
                padding-bottom: 4px;
                border-bottom: 1px solid #e5e7eb;
            }

            .theme-options {
                display: grid;
                gap: 8px;
            }

            .theme-option {
                display: flex;
                align-items: center;
                gap: 12px;
                padding: 12px;
                border-radius: 8px;
                cursor: pointer;
                transition: all 0.2s ease;
                border: 2px solid transparent;
            }

            .theme-option:hover {
                background: #f3f4f6;
            }

            .theme-option.active {
                background: #eff6ff;
                border-color: var(--theme-primary, #2563eb);
            }

            .theme-preview {
                width: 40px;
                height: 30px;
                border-radius: 6px;
                display: flex;
                overflow: hidden;
                border: 1px solid #e5e7eb;
            }

            .theme-color-primary,
            .theme-color-accent,
            .theme-color-surface {
                flex: 1;
                height: 100%;
            }

            .theme-info {
                flex: 1;
            }

            .theme-name {
                font-size: 14px;
                font-weight: 500;
                color: #374151;
                display: flex;
                align-items: center;
                gap: 6px;
                margin-bottom: 2px;
            }

            .theme-icon {
                font-size: 16px;
            }

            .theme-description {
                font-size: 12px;
                color: #6b7280;
            }

            .theme-switcher-footer {
                padding: 12px 20px;
                border-top: 1px solid #e5e7eb;
                background: #f9fafb;
                text-align: center;
            }

            /* 深色模式适配 */
            [data-theme="dark"] .theme-switcher-panel {
                background: #1e293b;
                border-color: #334155;
                color: #e2e8f0;
            }

            [data-theme="dark"] .theme-switcher-header {
                background: #0f172a;
                border-color: #334155;
            }

            [data-theme="dark"] .theme-switcher-header h4 {
                color: #e2e8f0;
            }

            [data-theme="dark"] .theme-option:hover {
                background: #334155;
            }

            [data-theme="dark"] .theme-option.active {
                background: #1e40af;
            }

            [data-theme="dark"] .theme-switcher-footer {
                background: #0f172a;
                border-color: #334155;
            }

            /* 响应式设计 */
            @media (max-width: 768px) {
                #theme-switcher-container {
                    top: 10px;
                    right: 10px;
                }

                .theme-switcher-panel {
                    width: 320px;
                    max-height: 500px;
                }

                .theme-switcher-trigger {
                    padding: 10px 14px;
                    font-size: 13px;
                }
            }
        `;

        document.head.appendChild(styles);
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 切换面板显示/隐藏
        document.addEventListener('click', (e) => {
            const trigger = document.getElementById('theme-switcher-trigger');
            const panel = document.getElementById('theme-switcher-panel');
            const close = document.getElementById('theme-switcher-close');

            if (trigger && trigger.contains(e.target)) {
                panel.classList.toggle('show');
            } else if (close && close.contains(e.target)) {
                panel.classList.remove('show');
            } else if (panel && !panel.contains(e.target)) {
                panel.classList.remove('show');
            }
        });

        // 主题选择
        document.addEventListener('click', (e) => {
            const themeOption = e.target.closest('.theme-option');
            if (themeOption) {
                const theme = themeOption.dataset.theme;
                this.switchTheme(theme);
            }
        });

        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.shiftKey && e.key === 'T') {
                e.preventDefault();
                const panel = document.getElementById('theme-switcher-panel');
                panel.classList.toggle('show');
            }
        });
    }

    /**
     * 切换主题
     */
    switchTheme(theme) {
        if (!this.themes[theme]) {
            console.warn(`主题 "${theme}" 不存在`);
            return;
        }

        this.currentTheme = theme;
        this.applyTheme(theme);
        this.saveTheme(theme);
        this.updateActiveState();

        // 触发自定义事件
        window.dispatchEvent(new CustomEvent('themeChanged', {
            detail: { theme, themeInfo: this.themes[theme] }
        }));

        // 显示切换成功提示
        this.showThemeChangeNotification(theme);
    }

    /**
     * 应用主题
     */
    applyTheme(theme) {
        document.documentElement.setAttribute('data-theme', theme);
        
        // 更新主题预览颜色
        this.updateThemePreviewColors();
    }

    /**
     * 更新主题预览颜色
     */
    updateThemePreviewColors() {
        const style = getComputedStyle(document.documentElement);
        
        Object.keys(this.themes).forEach(themeKey => {
            const preview = document.querySelector(`[data-theme="${themeKey}"] .theme-preview`);
            if (preview) {
                const primary = preview.querySelector('.theme-color-primary');
                const accent = preview.querySelector('.theme-color-accent');
                const surface = preview.querySelector('.theme-color-surface');

                // 临时应用主题来获取颜色
                document.documentElement.setAttribute('data-theme', themeKey);
                const tempStyle = getComputedStyle(document.documentElement);
                
                if (primary) primary.style.backgroundColor = tempStyle.getPropertyValue('--theme-primary').trim();
                if (accent) accent.style.backgroundColor = tempStyle.getPropertyValue('--theme-accent').trim();
                if (surface) surface.style.backgroundColor = tempStyle.getPropertyValue('--theme-surface').trim();
            }
        });

        // 恢复当前主题
        document.documentElement.setAttribute('data-theme', this.currentTheme);
    }

    /**
     * 更新激活状态
     */
    updateActiveState() {
        document.querySelectorAll('.theme-option').forEach(option => {
            option.classList.remove('active');
        });

        const activeOption = document.querySelector(`[data-theme="${this.currentTheme}"].theme-option`);
        if (activeOption) {
            activeOption.classList.add('active');
        }
    }

    /**
     * 显示主题切换通知
     */
    showThemeChangeNotification(theme) {
        const themeInfo = this.themes[theme];
        
        // 如果存在 toastr，使用它显示通知
        if (window.toastr) {
            toastr.success(`已切换到 ${themeInfo.icon} ${themeInfo.name} 主题`, '主题已更新');
        } else {
            // 否则使用简单的通知
            console.log(`主题已切换到: ${themeInfo.name}`);
        }
    }

    /**
     * 检测系统偏好
     */
    detectSystemPreference() {
        if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
            // 如果用户没有手动设置过主题，且系统偏好深色模式
            if (!localStorage.getItem('selectedTheme')) {
                this.switchTheme('dark');
            }
        }

        // 监听系统主题变化
        if (window.matchMedia) {
            window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
                if (!localStorage.getItem('selectedTheme')) {
                    this.switchTheme(e.matches ? 'dark' : 'primary');
                }
            });
        }
    }

    /**
     * 保存主题设置
     */
    saveTheme(theme) {
        localStorage.setItem('selectedTheme', theme);
    }

    /**
     * 加载主题设置
     */
    loadTheme() {
        return localStorage.getItem('selectedTheme') || 'primary';
    }

    /**
     * 获取当前主题信息
     */
    getCurrentTheme() {
        return {
            key: this.currentTheme,
            info: this.themes[this.currentTheme]
        };
    }

    /**
     * 获取所有可用主题
     */
    getAllThemes() {
        return this.themes;
    }
}

// 自动初始化
document.addEventListener('DOMContentLoaded', () => {
    window.advancedThemeSwitcher = new AdvancedThemeSwitcher();
});

// 导出供其他脚本使用
window.AdvancedThemeSwitcher = AdvancedThemeSwitcher;
