/* 卡片标题和H3标签字体大小自定义 */

/* H3标签全局字体大小控制 */
h3, .h3 {
    font-size: 1.4rem !important;   /* 覆盖AdminLTE默认的calc(1.3rem + 0.6vw) */
    font-weight: 600 !important;    /* 调整字体粗细 */
    line-height: 1.3 !important;    /* 调整行高 */
    margin-bottom: 0.75rem !important;
}

/* 大屏幕下的H3字体大小 */
@media (min-width: 1200px) {
    h3, .h3 {
        font-size: 1.6rem !important;  /* 覆盖AdminLTE默认的1.75rem */
    }
}

/* 全局卡片标题字体大小控制 */
.card-title {
    font-size: 1.25rem !important;  /* 调整为更大的字体 */
    font-weight: 600 !important;    /* 调整字体粗细 */
    line-height: 1.3 !important;    /* 调整行高 */
    margin-bottom: 0.5rem !important;
}

/* H3标签不同大小变体 */
.h3-sm, h3.h3-sm {
    font-size: 1.2rem !important;
    font-weight: 500 !important;
}

.h3-md, h3.h3-md {
    font-size: 1.4rem !important;
    font-weight: 600 !important;
}

.h3-lg, h3.h3-lg {
    font-size: 1.6rem !important;
    font-weight: 700 !important;
}

.h3-xl, h3.h3-xl {
    font-size: 1.8rem !important;
    font-weight: 700 !important;
}

/* 针对不同大小的卡片标题 */
.card-title.h1, .card-title.h2, .card-title.h3,
.card-title.h4, .card-title.h5, .card-title.h6 {
    font-size: inherit !important;
}

/* 卡片中的H3标题特殊处理 */
.card-header h3,
.card-body h3,
.card-footer h3 {
    margin-top: 0 !important;
    margin-bottom: 0.5rem !important;
}

.card-header h3.card-title {
    margin-bottom: 0 !important;
}

/* 小号卡片标题 */
.card-title-sm {
    font-size: 1rem !important;
    font-weight: 500 !important;
}

/* 中号卡片标题 */
.card-title-md {
    font-size: 1.125rem !important;
    font-weight: 600 !important;
}

/* 大号卡片标题 */
.card-title-lg {
    font-size: 1.375rem !important;
    font-weight: 700 !important;
}

/* 特大号卡片标题 */
.card-title-xl {
    font-size: 1.5rem !important;
    font-weight: 700 !important;
}

/* 响应式字体大小 */
@media (max-width: 768px) {
    .card-title {
        font-size: 1.125rem !important;
    }

    .card-title-lg {
        font-size: 1.25rem !important;
    }

    .card-title-xl {
        font-size: 1.375rem !important;
    }
}

@media (max-width: 576px) {
    .card-title {
        font-size: 1rem !important;
    }

    .card-title-lg {
        font-size: 1.125rem !important;
    }

    .card-title-xl {
        font-size: 1.25rem !important;
    }
}

/* 特定页面的卡片标题 */
.consumption-plan .card-title {
    font-size: 1.3rem !important;
    font-weight: 600 !important;
    color: var(--theme-primary, #007bff) !important;
}

/* 仪表盘卡片标题 */
.dashboard-card .card-title {
    font-size: 1.2rem !important;
    font-weight: 600 !important;
}

/* 详情页面卡片标题 */
.detail-page .card-title {
    font-size: 1.4rem !important;
    font-weight: 700 !important;
    color: #333 !important;
}

/* 表单卡片标题 */
.form-card .card-title {
    font-size: 1.25rem !important;
    font-weight: 600 !important;
    margin-bottom: 1rem !important;
}

/* 列表页面卡片标题 */
.list-page .card-title {
    font-size: 1.15rem !important;
    font-weight: 600 !important;
}

/* 主题适配 */
[data-theme="dark"] .card-title {
    color: var(--theme-text, #ffffff) !important;
}

[data-theme="primary"] .card-title {
    color: var(--theme-text, #333333) !important;
}

/* 卡片标题图标 */
.card-title i {
    margin-right: 0.5rem;
    font-size: 0.9em;
}

/* 卡片副标题 */
.card-subtitle {
    font-size: 0.9rem !important;
    font-weight: 400 !important;
    color: #6c757d !important;
    margin-top: -0.25rem !important;
    margin-bottom: 0.75rem !important;
}

/* 卡片工具栏按钮字体 */
.card-tools .btn {
    font-size: 0.875rem !important;
}

.card-tools .btn-sm {
    font-size: 0.8rem !important;
}

/* 卡片头部整体样式 */
.card-header {
    padding: 1rem 1.25rem !important;
}

.card-header .card-title {
    margin-bottom: 0 !important;
}

/* 紧凑型卡片标题 */
.card-compact .card-title {
    font-size: 1rem !important;
    margin-bottom: 0.25rem !important;
}

.card-compact .card-header {
    padding: 0.75rem 1rem !important;
}

/* 宽松型卡片标题 */
.card-spacious .card-title {
    font-size: 1.5rem !important;
    margin-bottom: 1rem !important;
}

.card-spacious .card-header {
    padding: 1.5rem 2rem !important;
}
