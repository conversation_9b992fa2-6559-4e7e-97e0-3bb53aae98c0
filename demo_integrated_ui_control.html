<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>集成界面控制演示 - 导航栏眼睛图标</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="app/static/css/theme-colors.css">
    <link rel="stylesheet" href="app/static/css/unified-page-styles.css">
    <link rel="stylesheet" href="app/static/css/table-readability-fix.css">
    <link rel="stylesheet" href="app/static/css/disable-blur-effects.css">
</head>
<body data-theme="primary">
    <!-- 导航栏 - 包含集成的界面控制 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-desktop"></i>
                集成界面控制演示
            </a>
            
            <div class="navbar-nav ml-auto">
                <!-- 这里会自动插入界面控制的眼睛图标 -->
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="themeDropdown" role="button" data-toggle="dropdown">
                        <i class="fas fa-palette"></i> 主题
                    </a>
                    <div class="dropdown-menu dropdown-menu-right">
                        <a class="dropdown-item" href="#" onclick="switchTheme('primary')">🌊 海洋蓝</a>
                        <a class="dropdown-item" href="#" onclick="switchTheme('success')">🌿 自然绿</a>
                        <a class="dropdown-item" href="#" onclick="switchTheme('warning')">🔥 活力橙</a>
                        <a class="dropdown-item" href="#" onclick="switchTheme('info')">💜 优雅紫</a>
                        <a class="dropdown-item" href="#" onclick="switchTheme('dark')">🌙 深色主题</a>
                    </div>
                </li>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <!-- 页面标题 -->
        <div class="row mb-4 page-header">
            <div class="col-md-8">
                <h2>集成界面控制系统</h2>
                <p class="text-muted">通过导航栏眼睛图标进行界面控制，不遮挡界面内容</p>
            </div>
            <div class="col-md-4">
                <div class="page-actions">
                    <button type="button" class="btn btn-info" onclick="showControlHelp()">
                        <i class="fas fa-question-circle"></i> 使用说明
                    </button>
                </div>
            </div>
        </div>

        <!-- 功能说明 -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-eye"></i> 界面控制特性</h5>
                    </div>
                    <div class="card-body">
                        <ul>
                            <li>✅ <strong>导航栏集成</strong>：眼睛图标，不遮挡界面</li>
                            <li>✅ <strong>快速模式</strong>：性能优先/平衡/视觉优先</li>
                            <li>✅ <strong>表格优化</strong>：修复文字背景融合问题</li>
                            <li>✅ <strong>对比度增强</strong>：自动修复可读性问题</li>
                            <li>✅ <strong>实时预览</strong>：设置立即生效</li>
                            <li>✅ <strong>设置保存</strong>：用户偏好持久化</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-keyboard"></i> 快捷操作</h5>
                    </div>
                    <div class="card-body">
                        <ul>
                            <li><kbd>Ctrl</kbd> + <kbd>Shift</kbd> + <kbd>E</kbd> - 打开界面控制</li>
                            <li><strong>性能优先模式</strong> - 最小资源占用</li>
                            <li><strong>平衡模式</strong> - 推荐设置（默认）</li>
                            <li><strong>视觉优先模式</strong> - 最佳视觉效果</li>
                            <li><strong>一键最佳设置</strong> - 基于采购订单页面</li>
                            <li><strong>设置导入导出</strong> - 配置备份恢复</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- 表格演示 - 展示修复效果 -->
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-table"></i> 表格可读性修复演示</h5>
                <small class="text-muted">展示文字与背景色融合问题的修复效果</small>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle"></i> 修复说明</h6>
                    <p class="mb-0">
                        原问题：表格中的文字与背景色融合，导致可读性差。<br>
                        解决方案：强制设置文字颜色为 #333333，背景色为半透明白色，确保足够的对比度。
                    </p>
                </div>
                
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>订单编号</th>
                                <th>客户名称</th>
                                <th>创建时间</th>
                                <th>金额</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>
                                    <a href="#" class="text-primary font-weight-medium">ORD-2024-001</a>
                                </td>
                                <td>张三公司</td>
                                <td class="datetime-column">
                                    <div class="datetime-display">
                                        <span class="date">2024-12-01</span>
                                        <span class="time">09:30</span>
                                    </div>
                                </td>
                                <td class="number-column">¥12,580.00</td>
                                <td>
                                    <span class="order-status status-confirmed">已确认</span>
                                </td>
                                <td class="action-column">
                                    <div class="btn-group-compact">
                                        <button class="btn btn-outline-primary btn-sm">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-outline-success btn-sm">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-outline-danger btn-sm">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <a href="#" class="text-primary font-weight-medium">ORD-2024-002</a>
                                </td>
                                <td>李四企业</td>
                                <td class="datetime-column">
                                    <div class="datetime-display">
                                        <span class="date">2024-12-01</span>
                                        <span class="time">14:15</span>
                                    </div>
                                </td>
                                <td class="number-column">¥8,960.50</td>
                                <td>
                                    <span class="order-status status-pending">待确认</span>
                                </td>
                                <td class="action-column">
                                    <div class="btn-group-compact">
                                        <button class="btn btn-outline-primary btn-sm">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-outline-success btn-sm">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-outline-danger btn-sm">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <a href="#" class="text-primary font-weight-medium">ORD-2024-003</a>
                                </td>
                                <td>王五集团</td>
                                <td class="datetime-column">
                                    <div class="datetime-display">
                                        <span class="date">2024-12-02</span>
                                        <span class="time">11:45</span>
                                    </div>
                                </td>
                                <td class="number-column">¥15,320.75</td>
                                <td>
                                    <span class="order-status status-delivered">已送达</span>
                                </td>
                                <td class="action-column">
                                    <div class="btn-group-compact">
                                        <button class="btn btn-outline-primary btn-sm">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-outline-success btn-sm">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-outline-danger btn-sm">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <a href="#" class="text-primary font-weight-medium">ORD-2024-004</a>
                                </td>
                                <td>赵六有限公司</td>
                                <td class="datetime-column">
                                    <div class="datetime-display">
                                        <span class="date">2024-12-02</span>
                                        <span class="time">16:20</span>
                                    </div>
                                </td>
                                <td class="number-column">¥6,750.25</td>
                                <td>
                                    <span class="order-status status-cancelled">已取消</span>
                                </td>
                                <td class="action-column">
                                    <div class="btn-group-compact">
                                        <button class="btn btn-outline-primary btn-sm">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-outline-success btn-sm" disabled>
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-outline-danger btn-sm">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- 修复效果对比 -->
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-danger text-white">
                        <h6><i class="fas fa-times"></i> 修复前的问题</h6>
                    </div>
                    <div class="card-body">
                        <ul>
                            <li>❌ 文字与背景色融合</li>
                            <li>❌ 表头对比度不足</li>
                            <li>❌ 链接颜色不明显</li>
                            <li>❌ 状态徽章可读性差</li>
                            <li>❌ 按钮边框模糊</li>
                            <li>❌ 焦点状态模糊</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h6><i class="fas fa-check"></i> 修复后的效果</h6>
                    </div>
                    <div class="card-body">
                        <ul>
                            <li>✅ 强制文字对比度 (#333333)</li>
                            <li>✅ 表头渐变背景增强</li>
                            <li>✅ 链接颜色清晰可见</li>
                            <li>✅ 状态徽章高对比度</li>
                            <li>✅ 按钮边框清晰</li>
                            <li>✅ 焦点状态清晰</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- 技术说明 -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-code"></i> 技术实现</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>CSS 修复策略</h6>
                                <pre><code>.table tbody td {
    color: #333333 !important;
    background-color: rgba(255, 255, 255, 0.95) !important;
}

.table thead th {
    background: linear-gradient(135deg, 
        var(--theme-primary) 0%, 
        var(--theme-primary-dark) 100%) !important;
    color: #ffffff !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
}</code></pre>
                            </div>
                            <div class="col-md-6">
                                <h6>JavaScript 控制逻辑</h6>
                                <pre><code>// 修复文字背景对比度
fixTextBackgroundContrast() {
    document.querySelectorAll('.table tbody td').forEach(td => {
        td.style.color = '#333333';
        td.style.backgroundColor = 'rgba(255, 255, 255, 0.9)';
    });
}

// 增强表头对比度
enhanceTableHeaders() {
    document.querySelectorAll('.table thead th').forEach(th => {
        th.style.background = 'linear-gradient(...)';
        th.style.color = 'white';
    });
}</code></pre>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="app/static/js/integrated-ui-controller.js"></script>
    
    <script>
        // 演示页面的辅助函数
        function switchTheme(theme) {
            document.documentElement.setAttribute('data-theme', theme);
            document.body.setAttribute('data-theme', theme);
            
            // 更新导航栏背景
            const navbar = document.querySelector('.navbar');
            if (navbar) {
                navbar.className = navbar.className.replace(/bg-\w+/, `bg-${theme}`);
            }
            
            showToast(`已切换到${getThemeName(theme)}主题`, 'success');
        }

        function getThemeName(theme) {
            const names = {
                'primary': '海洋蓝',
                'success': '自然绿',
                'warning': '活力橙',
                'info': '优雅紫',
                'dark': '深色'
            };
            return names[theme] || theme;
        }

        function showControlHelp() {
            const helpContent = `
                <div class="text-left">
                    <h6><i class="fas fa-eye"></i> 界面控制使用说明</h6>
                    <hr>
                    <p><strong>1. 导航栏眼睛图标</strong></p>
                    <p>点击导航栏右侧的 <i class="fas fa-eye"></i> 界面 按钮打开控制面板</p>
                    
                    <p><strong>2. 快速模式</strong></p>
                    <ul>
                        <li><span class="badge badge-success">性能优先</span> - 最小资源占用，适合低配设备</li>
                        <li><span class="badge badge-primary">平衡模式</span> - 推荐设置，性能与视觉平衡</li>
                        <li><span class="badge badge-info">视觉优先</span> - 最佳视觉效果，适合高配设备</li>
                    </ul>
                    
                    <p><strong>3. 快捷键</strong></p>
                    <p>按 <kbd>Ctrl</kbd> + <kbd>Shift</kbd> + <kbd>E</kbd> 快速打开界面控制</p>
                    
                    <p><strong>4. 自动修复</strong></p>
                    <p>系统会自动检测并修复表格文字与背景色融合等可读性问题</p>
                </div>
            `;
            
            // 创建模态框显示帮助
            const modal = document.createElement('div');
            modal.className = 'modal fade';
            modal.innerHTML = `
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">使用说明</h5>
                            <button type="button" class="close" data-dismiss="modal">
                                <span>&times;</span>
                            </button>
                        </div>
                        <div class="modal-body">
                            ${helpContent}
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-primary" data-dismiss="modal">知道了</button>
                        </div>
                    </div>
                </div>
            `;
            
            document.body.appendChild(modal);
            $(modal).modal('show');
            
            // 模态框关闭后移除
            $(modal).on('hidden.bs.modal', function() {
                document.body.removeChild(modal);
            });
        }

        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.className = `alert alert-${type} alert-dismissible fade show`;
            toast.style.cssText = `
                position: fixed;
                top: 70px;
                right: 20px;
                z-index: 10000;
                min-width: 250px;
                font-size: 14px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            `;
            toast.innerHTML = `
                <i class="fas fa-info-circle"></i> ${message}
                <button type="button" class="close" data-dismiss="alert">
                    <span>&times;</span>
                </button>
            `;
            
            document.body.appendChild(toast);
            
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 3000);
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🎨 集成界面控制演示页面已加载');
            
            // 显示欢迎提示
            setTimeout(() => {
                showToast('点击导航栏的眼睛图标打开界面控制', 'info');
            }, 2000);
        });
    </script>
</body>
</html>
