/* 表格可读性修复 - 解决文字与背景色融合问题 */

/* === 核心问题修复 === */

/* 1. 强制表格文字对比度 */
.table tbody td {
    color: #333333 !important;
    background-color: rgba(255, 255, 255, 0.95) !important;
    border-bottom: 1px solid #e5e7eb !important;
}

.table tbody tr:nth-child(even) td {
    background-color: rgba(248, 249, 250, 0.95) !important;
}

.table tbody tr:hover td {
    background-color: rgba(59, 130, 246, 0.08) !important;
    color: #1f2937 !important;
}

/* 2. 表头对比度增强 */
.table thead th {
    background: linear-gradient(135deg, var(--theme-primary, #2563eb) 0%, var(--theme-primary-dark, #1d4ed8) 100%) !important;
    color: #ffffff !important;
    font-weight: 600 !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
    border: none !important;
    font-size: 1.125rem !important;
    padding: 16px 12px !important;
}

/* 3. 链接文字清晰度 */
.table tbody td a {
    color: var(--theme-primary, #2563eb) !important;
    font-weight: 500 !important;
    text-decoration: none !important;
}

.table tbody td a:hover {
    color: var(--theme-primary-dark, #1d4ed8) !important;
    text-decoration: underline !important;
}

/* 4. 状态徽章对比度 */
.table tbody td .badge,
.table tbody td .order-status,
.table tbody td .status-badge {
    color: #ffffff !important;
    font-weight: 500 !important;
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2) !important;
}

/* 状态徽章具体颜色修复 */
.status-pending, .status-待确认 {
    background-color: #f59e0b !important;
    color: #000000 !important;
    text-shadow: none !important;
}

.status-confirmed, .status-已确认 {
    background-color: #0891b2 !important;
    color: #ffffff !important;
}

.status-delivered, .status-已送达 {
    background-color: #059669 !important;
    color: #ffffff !important;
}

.status-cancelled, .status-已取消 {
    background-color: #dc2626 !important;
    color: #ffffff !important;
}

.status-processing, .status-处理中 {
    background-color: #7c3aed !important;
    color: #ffffff !important;
}

/* 5. 数字列对比度 */
.table tbody td.number-column {
    color: #1f2937 !important;
    font-weight: 500 !important;
    font-family: 'Consolas', 'Monaco', monospace !important;
}

/* 6. 日期时间列对比度 */
.table tbody td.datetime-column {
    color: #374151 !important;
}

.datetime-display .date {
    color: #1f2937 !important;
    font-weight: 500 !important;
}

.datetime-display .time {
    color: #6b7280 !important;
    font-size: 0.9em !important;
}

/* 7. 操作按钮对比度 */
.table tbody td .btn {
    border-width: 1px !important;
    font-weight: 500 !important;
}

.table tbody td .btn-outline-primary {
    color: var(--theme-primary, #2563eb) !important;
    border-color: var(--theme-primary, #2563eb) !important;
}

.table tbody td .btn-outline-primary:hover {
    background-color: var(--theme-primary, #2563eb) !important;
    color: #ffffff !important;
}

.table tbody td .btn-outline-success {
    color: #059669 !important;
    border-color: #059669 !important;
}

.table tbody td .btn-outline-success:hover {
    background-color: #059669 !important;
    color: #ffffff !important;
}

.table tbody td .btn-outline-secondary {
    color: #6b7280 !important;
    border-color: #6b7280 !important;
}

.table tbody td .btn-outline-secondary:hover {
    background-color: #6b7280 !important;
    color: #ffffff !important;
}

.table tbody td .btn-outline-danger {
    color: #dc2626 !important;
    border-color: #dc2626 !important;
}

.table tbody td .btn-outline-danger:hover {
    background-color: #dc2626 !important;
    color: #ffffff !important;
}

/* === 特殊表格类型修复 === */

/* 紧凑表格 */
.table-compact tbody td {
    padding: 8px 10px !important;
    font-size: 1rem !important;
    line-height: 1.4 !important;
}

.table-compact thead th {
    padding: 12px 10px !important;
    font-size: 1rem !important;
}

/* 宽松表格 */
.table-spacious tbody td {
    padding: 16px 14px !important;
    font-size: 1.25rem !important;
    line-height: 1.5 !important;
}

.table-spacious thead th {
    padding: 20px 14px !important;
    font-size: 1.25rem !important;
}

/* === 响应式修复 === */

/* 移动端表格可读性 */
@media (max-width: 768px) {
    .table tbody td {
        font-size: 0.9rem !important;
        padding: 8px 6px !important;
    }
    
    .table thead th {
        font-size: 0.9rem !important;
        padding: 10px 6px !important;
    }
    
    .table tbody td .btn {
        font-size: 0.8rem !important;
        padding: 4px 6px !important;
    }
    
    .status-badge,
    .order-status {
        font-size: 0.75rem !important;
        padding: 2px 6px !important;
    }
}

/* 平板端表格可读性 */
@media (min-width: 769px) and (max-width: 1024px) {
    .table tbody td {
        font-size: 1rem !important;
        padding: 10px 8px !important;
    }
    
    .table thead th {
        font-size: 1rem !important;
        padding: 14px 8px !important;
    }
}

/* === 深色主题适配 === */

[data-theme="dark"] .table tbody td {
    color: #f9fafb !important;
    background-color: rgba(30, 41, 59, 0.95) !important;
}

[data-theme="dark"] .table tbody tr:nth-child(even) td {
    background-color: rgba(51, 65, 85, 0.95) !important;
}

[data-theme="dark"] .table tbody tr:hover td {
    background-color: rgba(59, 130, 246, 0.2) !important;
    color: #ffffff !important;
}

[data-theme="dark"] .table tbody td a {
    color: #60a5fa !important;
}

[data-theme="dark"] .table tbody td a:hover {
    color: #93c5fd !important;
}

[data-theme="dark"] .datetime-display .date {
    color: #f3f4f6 !important;
}

[data-theme="dark"] .datetime-display .time {
    color: #9ca3af !important;
}

/* === 特殊情况修复 === */

/* 修复表格内嵌套元素的对比度 */
.table tbody td .small,
.table tbody td small {
    color: #6b7280 !important;
    font-size: 0.875rem !important;
}

[data-theme="dark"] .table tbody td .small,
[data-theme="dark"] .table tbody td small {
    color: #9ca3af !important;
}

/* 修复表格内图标对比度 */
.table tbody td i {
    color: inherit !important;
}

.table tbody td .text-muted {
    color: #6b7280 !important;
}

[data-theme="dark"] .table tbody td .text-muted {
    color: #9ca3af !important;
}

/* 修复表格内输入框对比度 */
.table tbody td .form-control {
    background-color: #ffffff !important;
    color: #1f2937 !important;
    border: 1px solid #d1d5db !important;
}

[data-theme="dark"] .table tbody td .form-control {
    background-color: #374151 !important;
    color: #f9fafb !important;
    border: 1px solid #4b5563 !important;
}

/* === 打印样式优化 === */

@media print {
    .table tbody td {
        color: #000000 !important;
        background-color: #ffffff !important;
        border: 1px solid #000000 !important;
    }
    
    .table thead th {
        background: #f3f4f6 !important;
        color: #000000 !important;
        border: 1px solid #000000 !important;
        text-shadow: none !important;
    }
    
    .table tbody td a {
        color: #000000 !important;
        text-decoration: underline !important;
    }
    
    .status-badge,
    .order-status {
        background: #f3f4f6 !important;
        color: #000000 !important;
        border: 1px solid #000000 !important;
        text-shadow: none !important;
    }
}

/* === 无障碍优化 === */

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
    .table tbody td {
        color: #000000 !important;
        background-color: #ffffff !important;
        border: 2px solid #000000 !important;
    }
    
    .table thead th {
        background: #000000 !important;
        color: #ffffff !important;
        border: 2px solid #ffffff !important;
    }
    
    .table tbody td a {
        color: #0000ff !important;
        text-decoration: underline !important;
    }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
    .table tbody tr,
    .table tbody td,
    .table tbody td .btn {
        transition: none !important;
        transform: none !important;
    }
}
