#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
引导场景智能检测演示
"""

import sys
import os

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def demo_scenario_detection():
    """演示智能场景检测功能"""
    from flask import Flask
    from config import Config
    from flask_sqlalchemy import SQLAlchemy
    from app.services.scenario_guide_service import ScenarioGuideService
    
    app = Flask(__name__)
    app.config.from_object(Config)
    db = SQLAlchemy(app)
    
    with app.app_context():
        print("🎯 智能场景检测演示")
        print("=" * 50)
        
        # 测试学校名称
        test_schools = [
            "北京市朝阳区实验小学",
            "清华大学",
            "山村希望小学", 
            "北京职业技术学院",
            "育才中学",
            "北京大学附属中学",
            "乡村小学",
            "技工学校",
            "师范大学",
            "第一高级中学"
        ]
        
        print("测试学校名称及检测结果：")
        print("-" * 50)
        
        for school_name in test_schools:
            detected_type = ScenarioGuideService.detect_school_type(school_name)
            scenario_info = ScenarioGuideService.SCHOOL_TYPES.get(detected_type, {})
            scenario_name = scenario_info.get('name', '未知场景')
            
            print(f"🏫 {school_name}")
            print(f"   ➤ 检测类型: {detected_type}")
            print(f"   ➤ 场景名称: {scenario_name}")
            print(f"   ➤ 场景描述: {scenario_info.get('description', '无描述')}")
            print()

def demo_personalized_guide():
    """演示个性化引导计划生成"""
    from flask import Flask
    from config import Config
    from flask_sqlalchemy import SQLAlchemy
    from app.services.scenario_guide_service import ScenarioGuideService
    
    app = Flask(__name__)
    app.config.from_object(Config)
    db = SQLAlchemy(app)
    
    with app.app_context():
        print("\n🎯 个性化引导计划演示")
        print("=" * 50)
        
        # 为不同场景生成引导计划
        scenarios = ['primary', 'university', 'rural']
        
        for scenario_type in scenarios:
            scenario_info = ScenarioGuideService.SCHOOL_TYPES.get(scenario_type, {})
            plan = ScenarioGuideService.create_personalized_guide_plan(scenario_type)
            
            print(f"📋 {scenario_info.get('name', scenario_type)} 引导计划")
            print("-" * 30)
            print(f"预估时间: {plan['estimated_time']}")
            print(f"难度等级: {plan['difficulty_level']}")
            print(f"总步骤数: {len(plan['steps'])}")
            print()
            
            print("引导步骤:")
            for i, step in enumerate(plan['steps'][:5], 1):  # 只显示前5步
                print(f"  {i}. {step['title']}")
                print(f"     {step['description']}")
                print(f"     预估时间: {step['estimated_time']}")
                print()
            
            if len(plan['steps']) > 5:
                print(f"  ... 还有 {len(plan['steps']) - 5} 个步骤")
            print()

def demo_scenario_report():
    """演示场景适用性报告"""
    from flask import Flask
    from config import Config
    from flask_sqlalchemy import SQLAlchemy
    from app.services.scenario_guide_service import ScenarioGuideService
    
    app = Flask(__name__)
    app.config.from_object(Config)
    db = SQLAlchemy(app)
    
    with app.app_context():
        print("\n📊 场景适用性报告演示")
        print("=" * 50)
        
        scenarios = ['primary', 'middle', 'university']
        
        for scenario_type in scenarios:
            scenario_info = ScenarioGuideService.SCHOOL_TYPES.get(scenario_type, {})
            report = ScenarioGuideService.generate_scenario_report(scenario_type)
            
            print(f"📈 {scenario_info.get('name', scenario_type)} 适用性报告")
            print("-" * 30)
            print(f"适用性评分: {report['suitability_score']}/10")
            print(f"复杂度评分: {report['complexity_score']}/10")
            print()
            
            print("推荐理由:")
            for reason in report['recommendations']:
                print(f"  ✓ {reason}")
            print()
            
            print("注意事项:")
            for warning in report['warnings']:
                print(f"  ⚠️ {warning}")
            print()

if __name__ == '__main__':
    try:
        demo_scenario_detection()
        demo_personalized_guide()
        demo_scenario_report()
        
        print("🎉 演示完成！")
        print("\n📝 接下来您可以：")
        print("1. 访问 http://127.0.0.1:5000/admin/guide/scenarios 管理场景")
        print("2. 访问 http://127.0.0.1:5000/admin/guide/analytics 查看分析")
        print("3. 在注册时系统会自动检测学校类型并推荐合适的引导流程")
        
    except Exception as e:
        print(f"❌ 演示失败: {str(e)}")
        print("请确保应用正在运行并且数据库连接正常")
