<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>统一页面样式演示 - 基于采购订单页面</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="app/static/css/theme-colors.css">
    <link rel="stylesheet" href="app/static/css/table-optimization.css">
    <link rel="stylesheet" href="app/static/css/unified-page-styles.css">
    <link rel="stylesheet" href="app/static/css/disable-blur-effects.css">
</head>
<body data-theme="primary">
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-palette"></i>
                统一页面样式演示
            </a>
            <div class="navbar-nav ml-auto">
                <span class="navbar-text text-white">
                    基于采购订单页面的优秀设计
                </span>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <!-- 页面标题和操作按钮 -->
        <div class="row mb-4 page-header">
            <div class="col-md-8">
                <h2>统一页面样式系统</h2>
                <p class="text-muted">基于采购订单页面的优秀设计，统一所有页面的样式标准</p>
            </div>
            <div class="col-md-4">
                <div class="page-actions">
                    <button type="button" class="btn btn-primary" onclick="applyPurchaseOrderStyle()">
                        <i class="fas fa-magic"></i> 应用采购订单样式
                    </button>
                    <button type="button" class="btn btn-outline-primary" onclick="toggleUIPanel()">
                        <i class="fas fa-cog"></i> 自定义设置
                    </button>
                </div>
            </div>
        </div>

        <!-- 设计原则说明 -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-lightbulb"></i> 设计原则</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>📏 尺寸标准</h6>
                        <ul>
                            <li><strong>字体大小</strong>：1.125rem (18px)</li>
                            <li><strong>行高</strong>：1.4</li>
                            <li><strong>表格内边距</strong>：12px 10px</li>
                            <li><strong>表头内边距</strong>：16px 12px</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>🎨 视觉效果</h6>
                        <ul>
                            <li><strong>圆角半径</strong>：12px</li>
                            <li><strong>阴影效果</strong>：0 4px 6px rgba(0,0,0,0.05)</li>
                            <li><strong>悬停变换</strong>：scale(1.001)</li>
                            <li><strong>过渡时间</strong>：0.2s ease</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- 筛选工具栏演示 -->
        <div class="card mb-4 filter-toolbar">
            <div class="card-body">
                <form class="row">
                    <div class="col-md-3 mb-2">
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text">搜索</span>
                            </div>
                            <input type="text" class="form-control" placeholder="输入关键词搜索">
                        </div>
                    </div>
                    <div class="col-md-2 mb-2">
                        <select class="form-control">
                            <option value="">所有状态</option>
                            <option value="active">活跃</option>
                            <option value="inactive">非活跃</option>
                        </select>
                    </div>
                    <div class="col-md-3 mb-2">
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text">日期</span>
                            </div>
                            <input type="date" class="form-control">
                        </div>
                    </div>
                    <div class="col-md-2 mb-2">
                        <button type="submit" class="btn btn-primary btn-block">
                            <i class="fas fa-search"></i> 搜索
                        </button>
                    </div>
                    <div class="col-md-2 mb-2">
                        <button type="reset" class="btn btn-outline-secondary btn-block">
                            <i class="fas fa-undo"></i> 重置
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- 表格演示 -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-table"></i> 统一表格样式演示</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover table-compact">
                        <thead>
                            <tr>
                                <th style="width: 140px;">订单号</th>
                                <th style="width: 100px;">区域</th>
                                <th style="width: 120px;">创建时间</th>
                                <th style="width: 100px;">送货日期</th>
                                <th style="width: 90px;" class="number-column">总金额</th>
                                <th style="width: 80px;">状态</th>
                                <th style="width: 200px;" class="action-column">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>
                                    <a href="#" class="text-primary font-weight-medium">
                                        PO202412001
                                    </a>
                                </td>
                                <td class="text-content">华东区域</td>
                                <td class="datetime-column">
                                    <div class="datetime-display">
                                        <span class="date">2024-12-01</span>
                                        <span class="time">09:30</span>
                                    </div>
                                </td>
                                <td class="datetime-column">
                                    <div class="datetime-display">
                                        <span class="date">2024-12-03</span>
                                    </div>
                                </td>
                                <td class="number-column">¥12,580.00</td>
                                <td>
                                    <span class="order-status status-confirmed">已确认</span>
                                </td>
                                <td class="action-column">
                                    <div class="btn-group-compact">
                                        <button class="btn btn-outline-primary btn-sm" title="查看详情">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-outline-success btn-sm" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-outline-secondary btn-sm" title="打印">
                                            <i class="fas fa-print"></i>
                                        </button>
                                        <button class="btn btn-outline-danger btn-sm" title="删除">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <a href="#" class="text-primary font-weight-medium">
                                        PO202412002
                                    </a>
                                </td>
                                <td class="text-content">华南区域</td>
                                <td class="datetime-column">
                                    <div class="datetime-display">
                                        <span class="date">2024-12-01</span>
                                        <span class="time">14:15</span>
                                    </div>
                                </td>
                                <td class="datetime-column">
                                    <div class="datetime-display">
                                        <span class="date">2024-12-04</span>
                                    </div>
                                </td>
                                <td class="number-column">¥8,960.50</td>
                                <td>
                                    <span class="order-status status-pending">待确认</span>
                                </td>
                                <td class="action-column">
                                    <div class="btn-group-compact">
                                        <button class="btn btn-outline-primary btn-sm" title="查看详情">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-outline-success btn-sm" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-outline-secondary btn-sm" title="打印">
                                            <i class="fas fa-print"></i>
                                        </button>
                                        <button class="btn btn-outline-danger btn-sm" title="删除">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <a href="#" class="text-primary font-weight-medium">
                                        PO202412003
                                    </a>
                                </td>
                                <td class="text-content">华北区域</td>
                                <td class="datetime-column">
                                    <div class="datetime-display">
                                        <span class="date">2024-12-02</span>
                                        <span class="time">11:45</span>
                                    </div>
                                </td>
                                <td class="datetime-column">
                                    <div class="datetime-display">
                                        <span class="date">2024-12-05</span>
                                    </div>
                                </td>
                                <td class="number-column">¥15,320.75</td>
                                <td>
                                    <span class="order-status status-delivered">已送达</span>
                                </td>
                                <td class="action-column">
                                    <div class="btn-group-compact">
                                        <button class="btn btn-outline-primary btn-sm" title="查看详情">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-outline-success btn-sm" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-outline-secondary btn-sm" title="打印">
                                            <i class="fas fa-print"></i>
                                        </button>
                                        <button class="btn btn-outline-danger btn-sm" title="删除">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <a href="#" class="text-primary font-weight-medium">
                                        PO202412004
                                    </a>
                                </td>
                                <td class="text-content">西南区域</td>
                                <td class="datetime-column">
                                    <div class="datetime-display">
                                        <span class="date">2024-12-02</span>
                                        <span class="time">16:20</span>
                                    </div>
                                </td>
                                <td class="datetime-column">
                                    <div class="datetime-display">
                                        <span class="date">2024-12-06</span>
                                    </div>
                                </td>
                                <td class="number-column">¥6,750.25</td>
                                <td>
                                    <span class="order-status status-cancelled">已取消</span>
                                </td>
                                <td class="action-column">
                                    <div class="btn-group-compact">
                                        <button class="btn btn-outline-primary btn-sm" title="查看详情">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-outline-success btn-sm" title="编辑" disabled>
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-outline-secondary btn-sm" title="打印">
                                            <i class="fas fa-print"></i>
                                        </button>
                                        <button class="btn btn-outline-danger btn-sm" title="删除">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- 分页演示 -->
        <nav aria-label="页面导航">
            <ul class="pagination">
                <li class="page-item disabled">
                    <a class="page-link" href="#" tabindex="-1">上一页</a>
                </li>
                <li class="page-item active">
                    <a class="page-link" href="#">1</a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="#">2</a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="#">3</a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="#">下一页</a>
                </li>
            </ul>
        </nav>

        <!-- 特性说明 -->
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6><i class="fas fa-check-circle"></i> 统一特性</h6>
                    </div>
                    <div class="card-body">
                        <ul>
                            <li>✅ 统一的字体大小和行高</li>
                            <li>✅ 一致的表格样式和间距</li>
                            <li>✅ 标准化的按钮和操作区域</li>
                            <li>✅ 统一的状态徽章样式</li>
                            <li>✅ 响应式设计适配</li>
                            <li>✅ 无障碍友好的交互</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6><i class="fas fa-cogs"></i> 可定制选项</h6>
                    </div>
                    <div class="card-body">
                        <ul>
                            <li>🎨 字体大小：5个级别可选</li>
                            <li>📏 表格密度：紧凑/正常/宽松</li>
                            <li>🎯 间距控制：3种密度模式</li>
                            <li>🖼️ 背景样式：清洁/纹理/渐变</li>
                            <li>💳 卡片样式：平面/阴影/边框</li>
                            <li>📱 响应式优化开关</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="app/static/js/unified-ui-controller.js"></script>
    
    <script>
        // 演示页面的控制函数
        function applyPurchaseOrderStyle() {
            if (window.unifiedUIController) {
                window.unifiedUIController.applyPurchaseOrderStyle();
                showNotification('已应用采购订单页面样式', 'success');
            }
        }

        function toggleUIPanel() {
            const panel = document.getElementById('unified-ui-panel');
            if (panel) {
                panel.style.display = panel.style.display === 'none' ? 'block' : 'none';
            }
        }

        function showNotification(message, type) {
            const notification = document.createElement('div');
            notification.className = `alert alert-${type} alert-dismissible fade show`;
            notification.style.cssText = `
                position: fixed;
                top: 80px;
                left: 50%;
                transform: translateX(-50%);
                z-index: 10000;
                min-width: 300px;
                text-align: center;
            `;
            notification.innerHTML = `
                ${message}
                <button type="button" class="close" data-dismiss="alert">
                    <span>&times;</span>
                </button>
            `;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 3000);
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🎨 统一页面样式演示已加载');
            
            // 显示快捷键提示
            setTimeout(() => {
                showNotification('快捷键：Ctrl+Shift+U 打开界面控制面板', 'info');
            }, 2000);
        });
    </script>
</body>
</html>
