from flask import Blueprint, render_template
from flask_login import login_required

examples_bp = Blueprint('examples', __name__, url_prefix='/examples')

@examples_bp.route('/categorized-ingredient-select')
@login_required
def categorized_ingredient_select():
    """分类食材选择组件示例"""
    return render_template('examples/categorized_ingredient_select.html')

@examples_bp.route('/navigation-optimization')
def navigation_optimization():
    """导航菜单优化演示 - 直接访问入口"""
    return render_template('examples/navigation_optimization.html')
