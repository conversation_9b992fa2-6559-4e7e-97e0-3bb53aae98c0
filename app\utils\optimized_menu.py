"""
优化后的导航菜单配置
将原来的多个菜单项合并为更少、更有逻辑的分组
"""
from flask import url_for
from flask_login import current_user
from datetime import date, timedelta, datetime

# 优化后的菜单配置 - 从原来的9+个主菜单减少到5个
OPTIMIZED_MENU_CONFIG = [
    {
        'id': 'dashboard',
        'name': '工作台',
        'icon': 'fas fa-tachometer-alt',
        'url': 'main.index',
        'children': [
            {
                'id': 'dashboard_overview',
                'name': '总览仪表盘',
                'url': 'main.index',
                'icon': 'fas fa-chart-pie'
            },
            {
                'id': 'daily_management',
                'name': '今日管理',
                'url': 'daily_management.index',
                'icon': 'fas fa-calendar-day',
                'module': 'daily',
                'action': 'view'
            },
            {
                'id': 'quick_actions',
                'name': '快速操作',
                'url': '#',
                'icon': 'fas fa-bolt',
                'children': [
                    {
                        'id': 'quick_stock_in',
                        'name': '快速入库',
                        'url': 'stock_in.create',
                        'icon': 'fas fa-plus-circle'
                    },
                    {
                        'id': 'quick_purchase',
                        'name': '快速采购',
                        'url': 'purchase_order.create',
                        'icon': 'fas fa-shopping-cart'
                    }
                ]
            }
        ]
    },
    {
        'id': 'menu_planning',
        'name': '菜单计划',
        'icon': 'fas fa-utensils',
        'children': [
            {
                'id': 'weekly_menu_plan',
                'name': '周菜单规划',
                'url': 'weekly_menu_v2.plan',
                'icon': 'fas fa-calendar-alt',
                'module': 'weekly_menu',
                'action': 'edit'
            },
            {
                'id': 'recipe_management',
                'name': '食谱库',
                'url': 'recipe.index',
                'icon': 'fas fa-book',
                'module': 'recipe',
                'action': 'view'
            },
            {
                'id': 'weekly_menu_list',
                'name': '菜单历史',
                'url': 'weekly_menu_v2.index',
                'icon': 'fas fa-history',
                'module': 'weekly_menu',
                'action': 'view'
            },
            {
                'id': 'menu_sync',
                'name': '菜单同步',
                'url': 'menu_sync.index',
                'icon': 'fas fa-sync',
                'module': 'weekly_menu',
                'action': 'edit'
            }
        ]
    },
    {
        'id': 'supply_chain',
        'name': '供应链',
        'icon': 'fas fa-truck',
        'children': [
            # 采购管理
            {
                'id': 'purchase_orders',
                'name': '采购订单',
                'url': 'purchase_order.index',
                'icon': 'fas fa-file-invoice',
                'module': 'purchase',
                'action': 'view'
            },
            {
                'id': 'suppliers',
                'name': '供应商',
                'url': 'supplier.index',
                'icon': 'fas fa-building',
                'module': 'supplier',
                'action': 'view'
            },
            # 库存管理
            {
                'id': 'inventory_overview',
                'name': '库存总览',
                'url': 'inventory.index',
                'icon': 'fas fa-warehouse',
                'module': 'inventory',
                'action': 'view'
            },
            {
                'id': 'stock_operations',
                'name': '出入库',
                'url': '#',
                'icon': 'fas fa-exchange-alt',
                'children': [
                    {
                        'id': 'stock_in_list',
                        'name': '入库记录',
                        'url': 'stock_in.index',
                        'module': 'inventory',
                        'action': 'view'
                    },
                    {
                        'id': 'stock_out_list',
                        'name': '出库记录',
                        'url': 'stock_out.index',
                        'module': 'inventory',
                        'action': 'view'
                    }
                ]
            },
            {
                'id': 'warehouses',
                'name': '仓库管理',
                'url': 'warehouse.index',
                'icon': 'fas fa-store',
                'module': 'inventory',
                'action': 'view'
            }
        ]
    },
    {
        'id': 'food_safety',
        'name': '食安质控',
        'icon': 'fas fa-shield-alt',
        'children': [
            {
                'id': 'ingredients',
                'name': '食材档案',
                'url': 'ingredient.index',
                'icon': 'fas fa-carrot',
                'module': 'ingredient',
                'action': 'view'
            },
            {
                'id': 'food_safety_records',
                'name': '安全检查',
                'url': 'food_safety.index',
                'icon': 'fas fa-clipboard-check',
                'module': 'food_safety',
                'action': 'view'
            },
            {
                'id': 'traceability',
                'name': '食品溯源',
                'url': 'traceability.index',
                'icon': 'fas fa-search',
                'module': 'traceability',
                'action': 'view'
            },
            {
                'id': 'quality_reports',
                'name': '质量报告',
                'url': 'reports.quality',
                'icon': 'fas fa-chart-line',
                'module': 'report',
                'action': 'view'
            }
        ]
    },
    {
        'id': 'management',
        'name': '管理中心',
        'icon': 'fas fa-cogs',
        'children': [
            # 报表统计
            {
                'id': 'reports_overview',
                'name': '数据报表',
                'url': 'reports.index',
                'icon': 'fas fa-chart-bar',
                'module': 'report',
                'action': 'view'
            },
            {
                'id': 'financial_reports',
                'name': '财务分析',
                'url': 'reports.financial',
                'icon': 'fas fa-dollar-sign',
                'module': 'report',
                'action': 'view'
            },
            # 系统管理（只显示常用的）
            {
                'id': 'user_management',
                'name': '用户管理',
                'url': 'system.users',
                'icon': 'fas fa-users',
                'module': 'user',
                'action': 'view',
                'admin_only': True
            },
            {
                'id': 'system_settings',
                'name': '系统设置',
                'url': 'system.settings',
                'icon': 'fas fa-sliders-h',
                'module': 'setting',
                'action': 'view'
            },
            # 高级管理功能（折叠到子菜单）
            {
                'id': 'advanced_admin',
                'name': '高级管理',
                'url': '#',
                'icon': 'fas fa-tools',
                'admin_only': True,
                'children': [
                    {
                        'id': 'roles',
                        'name': '角色管理',
                        'url': 'system.roles',
                        'module': 'role',
                        'action': 'view',
                        'admin_only': True
                    },
                    {
                        'id': 'module_visibility',
                        'name': '模块可见性',
                        'url': 'system.module_visibility',
                        'admin_only': True
                    },
                    {
                        'id': 'data_management',
                        'name': '数据管理',
                        'url': 'admin_data.data_management',
                        'admin_only': True
                    },
                    {
                        'id': 'system_fix',
                        'name': '系统修复',
                        'url': 'system_fix.index',
                        'admin_only': True
                    },
                    {
                        'id': 'backups',
                        'name': '数据备份',
                        'url': 'system.backups',
                        'module': 'backup',
                        'action': 'view',
                        'admin_only': True
                    }
                ]
            }
        ]
    }
]

# 移动端简化菜单（只显示最重要的功能）
MOBILE_MENU_CONFIG = [
    {
        'id': 'dashboard',
        'name': '工作台',
        'icon': 'fas fa-tachometer-alt',
        'url': 'main.index'
    },
    {
        'id': 'daily_quick',
        'name': '今日管理',
        'icon': 'fas fa-calendar-day',
        'url': 'daily_management.index',
        'module': 'daily',
        'action': 'view'
    },
    {
        'id': 'menu_quick',
        'name': '菜单',
        'icon': 'fas fa-utensils',
        'url': 'weekly_menu_v2.plan',
        'module': 'weekly_menu',
        'action': 'view'
    },
    {
        'id': 'inventory_quick',
        'name': '库存',
        'icon': 'fas fa-warehouse',
        'url': 'inventory.index',
        'module': 'inventory',
        'action': 'view'
    },
    {
        'id': 'more',
        'name': '更多',
        'icon': 'fas fa-ellipsis-h',
        'children': [
            {
                'id': 'purchase_orders',
                'name': '采购订单',
                'url': 'purchase_order.index',
                'module': 'purchase',
                'action': 'view'
            },
            {
                'id': 'suppliers',
                'name': '供应商',
                'url': 'supplier.index',
                'module': 'supplier',
                'action': 'view'
            },
            {
                'id': 'reports',
                'name': '报表',
                'url': 'reports.index',
                'module': 'report',
                'action': 'view'
            },
            {
                'id': 'settings',
                'name': '设置',
                'url': 'system.settings',
                'module': 'setting',
                'action': 'view'
            }
        ]
    }
]

def get_url(item):
    """根据菜单项配置生成URL"""
    from datetime import date

    if 'url' not in item:
        return '#'

    # 处理URL参数
    if 'url_params' in item and item['url_params']:
        params = {}
        for key, value in item['url_params'].items():
            # 处理特殊值
            if value == 'today':
                params[key] = date.today().strftime('%Y-%m-%d')
            else:
                params[key] = value
        return url_for(item['url'], **params)

    return url_for(item['url'])

def get_optimized_menu(user, mobile=False):
    """根据用户权限和设备类型生成优化的菜单配置"""
    if not user.is_authenticated:
        return []

    # 选择菜单配置
    menu_config = MOBILE_MENU_CONFIG if mobile else OPTIMIZED_MENU_CONFIG

    # 导入ModuleVisibility模型
    try:
        from app.models_visibility import ModuleVisibility
    except ImportError:
        ModuleVisibility = None

    result = []

    def filter_menu_item(item):
        """过滤菜单项"""
        # 检查管理员权限
        if item.get('admin_only', False) and not user.is_admin:
            return None

        # 检查模块权限
        if 'module' in item and 'action' in item:
            if not user.has_permission(item['module'], item['action']):
                return None

        # 检查模块可见性
        if ModuleVisibility and 'module' in item:
            visibility = ModuleVisibility.query.filter_by(
                school_id=user.school_id,
                module_name=item['module']
            ).first()
            if visibility and not visibility.is_visible:
                return None

        # 处理子菜单
        filtered_item = item.copy()
        if 'children' in item:
            filtered_children = []
            for child in item['children']:
                filtered_child = filter_menu_item(child)
                if filtered_child:
                    filtered_children.append(filtered_child)
            
            # 如果没有可见的子菜单，则隐藏父菜单（除非父菜单本身有URL）
            if not filtered_children and not item.get('url'):
                return None
            
            filtered_item['children'] = filtered_children

        return filtered_item

    for item in menu_config:
        filtered_item = filter_menu_item(item)
        if filtered_item:
            result.append(filtered_item)

    return result

def get_menu_statistics():
    """获取菜单优化统计信息"""
    from app.utils.menu import MENU_CONFIG
    
    # 统计原菜单
    original_main_count = len(MENU_CONFIG)
    original_total_count = 0
    for item in MENU_CONFIG:
        original_total_count += 1
        if 'children' in item:
            original_total_count += len(item['children'])
    
    # 统计优化后菜单
    optimized_main_count = len(OPTIMIZED_MENU_CONFIG)
    optimized_total_count = 0
    for item in OPTIMIZED_MENU_CONFIG:
        optimized_total_count += 1
        if 'children' in item:
            optimized_total_count += len(item['children'])
            # 统计二级子菜单
            for child in item['children']:
                if 'children' in child:
                    optimized_total_count += len(child['children'])
    
    return {
        'original': {
            'main_menus': original_main_count,
            'total_items': original_total_count
        },
        'optimized': {
            'main_menus': optimized_main_count,
            'total_items': optimized_total_count
        },
        'reduction': {
            'main_menus': original_main_count - optimized_main_count,
            'percentage': round((original_main_count - optimized_main_count) / original_main_count * 100, 1)
        }
    }
