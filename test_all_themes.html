<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>主题测试页面</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="app/static/css/theme-colors.css">
    <link rel="stylesheet" href="app/static/css/enhanced-theme-switcher.css">
    <style>
        body {
            background-color: var(--theme-surface, #ffffff);
            color: var(--theme-text, #111827);
            transition: all 0.3s ease;
        }
        
        .theme-test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin: 2rem 0;
        }
        
        .theme-test-card {
            padding: 1rem;
            border-radius: 8px;
            background-color: var(--theme-surface, #ffffff);
            border: 2px solid var(--theme-border, #e5e7eb);
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .theme-test-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        
        .theme-test-card.active {
            border-color: var(--theme-primary, #2563eb);
            background-color: var(--theme-primary-50, #eff6ff);
        }
        
        .theme-preview-large {
            width: 60px;
            height: 40px;
            border-radius: 8px;
            margin: 0 auto 0.5rem;
            border: 2px solid rgba(255,255,255,0.2);
            position: relative;
            overflow: hidden;
        }
        
        .theme-accent-large {
            position: absolute;
            top: 4px;
            right: 4px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            border: 1px solid rgba(255,255,255,0.3);
        }
        
        .current-theme-info {
            background: var(--theme-primary, #2563eb);
            color: white;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 2rem;
        }
        
        .test-components {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin: 2rem 0;
        }
        
        .component-test {
            padding: 1.5rem;
            background: var(--theme-surface, #ffffff);
            border-radius: 12px;
            border: 1px solid var(--theme-border, #e5e7eb);
        }
    </style>
</head>
<body data-theme="primary">
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-palette"></i>
                主题测试页面
            </a>
            <div class="navbar-nav ml-auto">
                <span class="navbar-text text-white" id="current-theme-display">
                    当前主题: 🌊 海洋蓝主题
                </span>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="current-theme-info">
            <h2 class="mb-0">
                <i class="fas fa-palette"></i>
                主题完整性测试
            </h2>
            <p class="mb-0 mt-2">点击下方任意主题卡片来测试主题切换功能</p>
        </div>

        <div class="row">
            <div class="col-12">
                <h3>🎨 所有可用主题</h3>
                <div class="theme-test-grid" id="theme-grid">
                    <!-- 主题卡片将通过JavaScript动态生成 -->
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <h3>🧪 组件测试</h3>
                <div class="test-components">
                    <div class="component-test">
                        <h5>按钮测试</h5>
                        <button class="btn btn-primary mr-2">主要按钮</button>
                        <button class="btn btn-secondary mr-2">次要按钮</button>
                        <button class="btn btn-success mr-2">成功按钮</button>
                        <button class="btn btn-warning mr-2">警告按钮</button>
                        <button class="btn btn-danger">危险按钮</button>
                    </div>
                    
                    <div class="component-test">
                        <h5>表单测试</h5>
                        <div class="form-group">
                            <label>输入框</label>
                            <input type="text" class="form-control" placeholder="请输入内容">
                        </div>
                        <div class="form-group">
                            <label>选择框</label>
                            <select class="form-control">
                                <option>选项 1</option>
                                <option>选项 2</option>
                                <option>选项 3</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="component-test">
                        <h5>卡片测试</h5>
                        <div class="card">
                            <div class="card-header">
                                卡片标题
                            </div>
                            <div class="card-body">
                                <p class="card-text">这是一个测试卡片的内容区域。</p>
                                <a href="#" class="btn btn-primary">操作按钮</a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="component-test">
                        <h5>徽章和进度条</h5>
                        <div class="mb-3">
                            <span class="badge badge-primary mr-2">主要</span>
                            <span class="badge badge-success mr-2">成功</span>
                            <span class="badge badge-warning mr-2">警告</span>
                            <span class="badge badge-danger">危险</span>
                        </div>
                        <div class="progress mb-2">
                            <div class="progress-bar" style="width: 75%"></div>
                        </div>
                        <small class="text-muted">进度: 75%</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // 主题定义（与theme-switcher.js保持一致）
        const themes = {
            'primary': { name: '🌊 海洋蓝主题', color: '#001F3F', accent: '#FF6B6B', category: 'professional' },
            'secondary': { name: '🔘 现代灰主题', color: '#475569', accent: '#F7FAFC', category: 'professional' },
            'modern': { name: '🚀 现代科技主题', color: '#1E1E2E', accent: '#00F5D4', category: 'tech' },
            'dark': { name: '🌙 深色主题', color: '#4F46E5', accent: '#10B981', category: 'dark' },
            'success': { name: '🌿 自然绿主题', color: '#4A7856', accent: '#BC8A5F', category: 'natural' },
            'nature': { name: '🍃 生态绿主题', color: '#22C55E', accent: '#F59E0B', category: 'natural' },
            'warning': { name: '🔥 活力橙主题', color: '#FF6B35', accent: '#0353A4', category: 'vibrant' },
            'energy': { name: '⚡ 能量橙主题', color: '#F59E0B', accent: '#3B82F6', category: 'vibrant' },
            'info': { name: '💜 优雅紫主题', color: '#7B5CBF', accent: '#22C55E', category: 'elegant' },
            'elegant': { name: '🎨 艺术紫主题', color: '#8B5CF6', accent: '#F59E0B', category: 'elegant' },
            'danger': { name: '❤️ 深邃红主题', color: '#C91F37', accent: '#F87171', category: 'alert' },
            'classic-neutral': { name: '🏛️ 经典中性风', color: '#8B4513', accent: '#DAA520', category: 'neutral' },
            'modern-neutral': { name: '⚪ 现代中性风', color: '#475569', accent: '#F7FAFC', category: 'neutral' },
            'vintage-elegant': { name: '🍷 复古典雅风', color: '#004225', accent: '#8B4513', category: 'vintage' },
            'noble-elegant': { name: '👑 贵族典雅风', color: '#191970', accent: '#663399', category: 'vintage' },
            'fresh-elegant': { name: '🌸 清新优雅风', color: '#4682B4', accent: '#FF69B4', category: 'vintage' },
            'spring-fresh': { name: '🌱 春日清新风', color: '#228B22', accent: '#FFD700', category: 'vintage' },
            'luxury-solemn': { name: '✨ 华丽庄重风', color: '#FFD700', accent: '#DC143C', category: 'vintage' },
            'royal-solemn': { name: '🎭 皇室庄重风', color: '#4B0082', accent: '#8B0000', category: 'vintage' }
        };

        let currentTheme = 'primary';

        // 生成主题网格
        function generateThemeGrid() {
            const grid = document.getElementById('theme-grid');
            grid.innerHTML = '';

            Object.entries(themes).forEach(([key, theme]) => {
                const card = document.createElement('div');
                card.className = `theme-test-card ${key === currentTheme ? 'active' : ''}`;
                card.setAttribute('data-theme', key);
                
                card.innerHTML = `
                    <div class="theme-preview-large" style="background: ${theme.color};">
                        <div class="theme-accent-large" style="background: ${theme.accent};"></div>
                    </div>
                    <div class="theme-name">${theme.name}</div>
                    <small class="text-muted">${theme.category}</small>
                `;
                
                card.addEventListener('click', () => switchTheme(key));
                grid.appendChild(card);
            });
        }

        // 切换主题
        function switchTheme(themeKey) {
            if (!themes[themeKey]) return;

            currentTheme = themeKey;
            
            // 应用主题
            document.documentElement.setAttribute('data-theme', themeKey);
            document.body.setAttribute('data-theme', themeKey);
            
            // 更新导航栏
            const navbar = document.querySelector('.navbar');
            if (navbar) {
                // 移除所有背景色类
                const bgClasses = ['bg-primary', 'bg-secondary', 'bg-success', 'bg-warning', 'bg-info', 'bg-danger', 'bg-dark',
                                  'bg-modern', 'bg-nature', 'bg-energy', 'bg-elegant',
                                  'bg-classic-neutral', 'bg-modern-neutral', 'bg-vintage-elegant', 'bg-noble-elegant',
                                  'bg-fresh-elegant', 'bg-spring-fresh', 'bg-luxury-solemn', 'bg-royal-solemn'];
                bgClasses.forEach(cls => navbar.classList.remove(cls));
                navbar.classList.add(`bg-${themeKey}`);
            }
            
            // 更新当前主题显示
            document.getElementById('current-theme-display').textContent = `当前主题: ${themes[themeKey].name}`;
            
            // 更新活跃状态
            document.querySelectorAll('.theme-test-card').forEach(card => {
                card.classList.remove('active');
            });
            document.querySelector(`[data-theme="${themeKey}"]`).classList.add('active');
            
            console.log(`主题已切换到: ${themes[themeKey].name}`);
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            generateThemeGrid();
            
            // 测试所有主题是否正常工作
            console.log('🎨 主题测试页面已加载');
            console.log(`📊 共有 ${Object.keys(themes).length} 个主题可供测试`);
            
            // 显示主题分类统计
            const categories = {};
            Object.values(themes).forEach(theme => {
                categories[theme.category] = (categories[theme.category] || 0) + 1;
            });
            console.log('📈 主题分类统计:', categories);
        });

        // 键盘快捷键测试
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.shiftKey) {
                const themeKeys = Object.keys(themes);
                const currentIndex = themeKeys.indexOf(currentTheme);
                let nextIndex;
                
                if (e.key === 'ArrowRight') {
                    // 下一个主题
                    nextIndex = (currentIndex + 1) % themeKeys.length;
                    switchTheme(themeKeys[nextIndex]);
                    e.preventDefault();
                } else if (e.key === 'ArrowLeft') {
                    // 上一个主题
                    nextIndex = currentIndex === 0 ? themeKeys.length - 1 : currentIndex - 1;
                    switchTheme(themeKeys[nextIndex]);
                    e.preventDefault();
                }
            }
        });
    </script>
</body>
</html>
