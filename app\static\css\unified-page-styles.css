/* 统一页面样式系统 - 基于采购订单页面的优秀设计 */

/* === 核心设计原则 === */
:root {
    /* 基于采购订单页面的优秀参数 */
    --unified-font-size: 1.125rem;        /* 18px - 采购订单页面的字体大小 */
    --unified-line-height: 1.4;           /* 行高 */
    --unified-table-padding: 12px 10px;   /* 表格单元格内边距 */
    --unified-header-padding: 16px 12px;  /* 表头内边距 */
    --unified-header-font-size: 1.125rem; /* 表头字体大小 */
    --unified-border-radius: 12px;        /* 圆角半径 */
    --unified-shadow: 0 4px 6px rgba(0, 0, 0, 0.05); /* 阴影 */
    --unified-hover-transform: scale(1.001); /* 悬停变换 */
    --unified-transition: all 0.2s ease;  /* 过渡效果 */
}

/* === 页面布局统一 === */

/* 页面容器 */
.container-fluid {
    padding: 1.5rem;
}

/* 页面标题区域 */
.page-header {
    margin-bottom: 2rem;
}

.page-header h2 {
    font-size: 1.75rem;
    font-weight: 600;
    color: var(--theme-text, #333);
    margin-bottom: 0.5rem;
}

.page-header .text-muted {
    font-size: 1rem;
    color: var(--theme-text-secondary, #6c757d);
}

/* 操作按钮区域 */
.page-actions {
    display: flex;
    gap: 0.75rem;
    align-items: center;
    justify-content: flex-end;
}

/* === 卡片统一样式 === */

.card {
    border: none;
    border-radius: var(--unified-border-radius);
    box-shadow: var(--unified-shadow);
    margin-bottom: 1.5rem;
}

.card-header {
    background: linear-gradient(135deg, var(--theme-surface, #f8f9fa) 0%, var(--theme-surface-dark, #e9ecef) 100%);
    border-bottom: 1px solid var(--theme-border, #dee2e6);
    padding: 1rem 1.25rem;
    border-radius: var(--unified-border-radius) var(--unified-border-radius) 0 0;
}

.card-body {
    padding: 1.25rem;
}

/* === 表格统一样式（基于采购订单页面） === */

/* 表格容器 */
.table-responsive {
    border-radius: var(--unified-border-radius);
    box-shadow: var(--unified-shadow);
    overflow: hidden;
    margin-bottom: 1.5rem;
}

/* 基础表格样式 */
.table {
    margin-bottom: 0;
    font-size: var(--unified-font-size);
    line-height: var(--unified-line-height);
}

/* 表头统一样式 */
.table thead {
    background: linear-gradient(135deg, var(--theme-primary) 0%, var(--theme-primary-light) 100%);
}

.table thead th {
    background: transparent;
    color: white;
    font-weight: 600;
    font-size: var(--unified-header-font-size);
    padding: var(--unified-header-padding);
    border: none;
    vertical-align: middle;
    white-space: nowrap;
    text-transform: none;
    letter-spacing: 0.3px;
}

/* 表格行统一样式 */
.table tbody tr {
    transition: var(--unified-transition);
    border-bottom: 1px solid var(--theme-border, #e5e7eb);
}

.table tbody tr:hover {
    background-color: rgba(var(--theme-primary-rgb, 59, 130, 246), 0.03);
    transform: var(--unified-hover-transform);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* 表格单元格统一样式 */
.table tbody td {
    padding: var(--unified-table-padding);
    vertical-align: middle;
    border-top: none;
    font-size: var(--unified-font-size);
    line-height: var(--unified-line-height);
}

/* === 特殊列样式 === */

/* 数字列 */
.number-column {
    text-align: right;
    font-family: 'Consolas', 'Monaco', monospace;
    font-weight: 500;
}

/* 操作列 */
.action-column {
    text-align: center;
    white-space: nowrap;
}

/* 日期时间列 */
.datetime-column {
    font-family: 'Consolas', 'Monaco', monospace;
    font-size: 0.95em;
}

.datetime-display {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.datetime-display .date {
    font-weight: 500;
    color: var(--theme-text, #333);
}

.datetime-display .time {
    font-size: 0.85em;
    color: var(--theme-text-secondary, #6c757d);
}

/* 文本内容列 */
.text-content {
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* === 状态徽章统一样式 === */

.order-status,
.status-badge {
    font-size: 0.85em;
    padding: 0.25em 0.6em;
    border-radius: 0.25rem;
    font-weight: 500;
    text-align: center;
    min-width: 60px;
    display: inline-block;
}

/* 状态颜色 */
.status-pending, .status-待确认 {
    background-color: #ffc107;
    color: #000;
}

.status-confirmed, .status-已确认 {
    background-color: #17a2b8;
    color: #fff;
}

.status-delivered, .status-已送达 {
    background-color: #28a745;
    color: #fff;
}

.status-cancelled, .status-已取消 {
    background-color: #dc3545;
    color: #fff;
}

.status-processing, .status-处理中 {
    background-color: #6f42c1;
    color: #fff;
}

/* === 按钮组统一样式 === */

.btn-group-compact .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    border-radius: 0.25rem;
    margin: 0 1px;
}

.btn-group-compact .btn i {
    font-size: 0.875rem;
}

/* === 筛选工具栏统一样式 === */

.filter-toolbar {
    background: linear-gradient(135deg, var(--theme-surface, #f8f9fa) 0%, var(--theme-surface-dark, #e9ecef) 100%);
    border: 1px solid var(--theme-border, #dee2e6);
    border-radius: var(--unified-border-radius);
    padding: 1rem 1.25rem;
    margin-bottom: 1.5rem;
}

.filter-toolbar .form-control {
    font-size: 0.9rem;
    height: calc(1.5em + 0.75rem + 2px);
}

.filter-toolbar .input-group-text {
    font-size: 0.875rem;
    background-color: var(--theme-surface-dark, #e9ecef);
    border-color: var(--theme-border, #ced4da);
}

/* === 分页统一样式 === */

.pagination {
    justify-content: center;
    margin-top: 2rem;
}

.pagination .page-link {
    font-size: 0.9rem;
    padding: 0.5rem 0.75rem;
    border-radius: 0.375rem;
    margin: 0 2px;
    border: 1px solid var(--theme-border, #dee2e6);
    color: var(--theme-text, #495057);
}

.pagination .page-link:hover {
    background-color: var(--theme-primary-50, #eff6ff);
    border-color: var(--theme-primary, #3b82f6);
    color: var(--theme-primary, #3b82f6);
}

.pagination .page-item.active .page-link {
    background-color: var(--theme-primary, #3b82f6);
    border-color: var(--theme-primary, #3b82f6);
}

/* === 空状态统一样式 === */

.table-empty {
    text-align: center;
    padding: 3rem 2rem;
    color: var(--theme-text-secondary, #6c757d);
}

.table-empty i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.table-empty h5 {
    font-size: 1.25rem;
    margin-bottom: 0.5rem;
    color: var(--theme-text, #495057);
}

.table-empty p {
    font-size: 0.9rem;
    margin-bottom: 0;
}

/* === 链接统一样式 === */

.text-primary.font-weight-medium {
    font-weight: 500;
    text-decoration: none;
    color: var(--theme-primary, #3b82f6);
}

.text-primary.font-weight-medium:hover {
    text-decoration: underline;
    color: var(--theme-primary-dark, #1d4ed8);
}

/* === 响应式优化 === */

/* 移动端优化 */
@media (max-width: 768px) {
    :root {
        --unified-font-size: 0.9rem;
        --unified-header-font-size: 0.9rem;
        --unified-table-padding: 8px 6px;
        --unified-header-padding: 12px 8px;
    }
    
    .container-fluid {
        padding: 1rem;
    }
    
    .page-header h2 {
        font-size: 1.5rem;
    }
    
    .page-actions {
        flex-direction: column;
        align-items: stretch;
        gap: 0.5rem;
    }
    
    .btn-group {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .table tbody td.action-column {
        width: 80px;
    }
    
    .btn-group-compact .btn {
        padding: 2px 4px;
        font-size: 0.7rem;
    }
    
    .text-content {
        max-width: 120px;
    }
}

/* 平板端优化 */
@media (min-width: 769px) and (max-width: 1024px) {
    :root {
        --unified-font-size: 1rem;
        --unified-header-font-size: 1rem;
        --unified-table-padding: 10px 8px;
        --unified-header-padding: 14px 10px;
    }
}

/* === 打印样式优化 === */

@media print {
    .page-actions,
    .filter-toolbar,
    .pagination,
    .btn-group-compact {
        display: none !important;
    }
    
    .table {
        font-size: 0.8rem;
    }
    
    .table thead th {
        background-color: var(--theme-primary, #3b82f6) !important;
        color: white !important;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #dee2e6;
    }
}
