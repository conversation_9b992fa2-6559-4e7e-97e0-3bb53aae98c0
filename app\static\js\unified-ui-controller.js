/**
 * 统一界面控制器 - 基于采购订单页面的优秀设计
 * 管理表格、字体、间距、背景等所有界面元素
 */

class UnifiedUIController {
    constructor() {
        this.settings = {
            // 字体控制
            fontSize: 'normal',           // xs, sm, normal, lg, xl
            fontScale: 1.0,              // 0.8 - 1.4
            
            // 表格控制
            tableStyle: 'unified',        // unified, compact, spacious, minimal
            tableDensity: 'normal',       // compact, normal, spacious
            showTableBorders: true,
            showTableHover: true,
            
            // 间距控制
            spacing: 'normal',            // compact, normal, spacious
            cardPadding: 'normal',        // sm, normal, lg
            
            // 背景控制
            backgroundStyle: 'clean',     // clean, textured, gradient
            cardStyle: 'elevated',        // flat, elevated, outlined
            
            // 颜色控制
            useGradientHeaders: true,
            headerStyle: 'gradient',      // solid, gradient, minimal
            
            // 响应式控制
            mobileOptimized: true,
            autoScale: true
        };
        
        this.init();
    }

    init() {
        this.loadSettings();
        this.applySettings();
        this.createControlPanel();
        this.bindEvents();
        this.initializePageElements();
    }

    // 加载用户设置
    loadSettings() {
        const saved = localStorage.getItem('unifiedUISettings');
        if (saved) {
            this.settings = { ...this.settings, ...JSON.parse(saved) };
        }
    }

    // 保存用户设置
    saveSettings() {
        localStorage.setItem('unifiedUISettings', JSON.stringify(this.settings));
    }

    // 应用设置
    applySettings() {
        const root = document.documentElement;
        const body = document.body;
        
        // 应用字体设置
        this.applyFontSettings(root);
        
        // 应用表格设置
        this.applyTableSettings(body);
        
        // 应用间距设置
        this.applySpacingSettings(body);
        
        // 应用背景设置
        this.applyBackgroundSettings(body);
        
        // 应用响应式设置
        this.applyResponsiveSettings(body);
        
        console.log('统一界面设置已应用:', this.settings);
    }

    // 应用字体设置
    applyFontSettings(root) {
        const fontScales = {
            'xs': 0.8,
            'sm': 0.9,
            'normal': 1.0,
            'lg': 1.1,
            'xl': 1.2
        };
        
        const scale = fontScales[this.settings.fontSize] || 1.0;
        root.style.setProperty('--unified-font-scale', scale);
        root.style.setProperty('--unified-font-size', `${1.125 * scale}rem`);
        root.style.setProperty('--unified-header-font-size', `${1.125 * scale}rem`);
        
        // 应用到所有表格
        document.querySelectorAll('.table').forEach(table => {
            table.style.fontSize = `${1.125 * scale}rem`;
        });
    }

    // 应用表格设置
    applyTableSettings(body) {
        // 移除所有表格样式类
        body.classList.remove('table-style-unified', 'table-style-compact', 'table-style-spacious', 'table-style-minimal');
        body.classList.remove('table-density-compact', 'table-density-normal', 'table-density-spacious');
        
        // 应用新的表格样式
        body.classList.add(`table-style-${this.settings.tableStyle}`);
        body.classList.add(`table-density-${this.settings.tableDensity}`);
        
        // 表格边框控制
        document.querySelectorAll('.table').forEach(table => {
            if (this.settings.showTableBorders) {
                table.classList.add('table-bordered');
            } else {
                table.classList.remove('table-bordered');
            }
            
            if (this.settings.showTableHover) {
                table.classList.add('table-hover');
            } else {
                table.classList.remove('table-hover');
            }
        });
    }

    // 应用间距设置
    applySpacingSettings(body) {
        const spacingValues = {
            'compact': {
                base: '0.75rem',
                lg: '1rem',
                xl: '1.5rem'
            },
            'normal': {
                base: '1rem',
                lg: '1.5rem',
                xl: '2rem'
            },
            'spacious': {
                base: '1.5rem',
                lg: '2rem',
                xl: '3rem'
            }
        };
        
        const spacing = spacingValues[this.settings.spacing];
        const root = document.documentElement;
        
        root.style.setProperty('--unified-spacing-base', spacing.base);
        root.style.setProperty('--unified-spacing-lg', spacing.lg);
        root.style.setProperty('--unified-spacing-xl', spacing.xl);
        
        // 应用卡片内边距
        const cardPaddingValues = {
            'sm': '0.75rem',
            'normal': '1.25rem',
            'lg': '2rem'
        };
        
        const cardPadding = cardPaddingValues[this.settings.cardPadding];
        document.querySelectorAll('.card-body').forEach(cardBody => {
            cardBody.style.padding = cardPadding;
        });
    }

    // 应用背景设置
    applyBackgroundSettings(body) {
        // 移除所有背景样式类
        body.classList.remove('bg-style-clean', 'bg-style-textured', 'bg-style-gradient');
        body.classList.remove('card-style-flat', 'card-style-elevated', 'card-style-outlined');
        
        // 应用新的背景样式
        body.classList.add(`bg-style-${this.settings.backgroundStyle}`);
        body.classList.add(`card-style-${this.settings.cardStyle}`);
        
        // 表头样式控制
        if (this.settings.useGradientHeaders) {
            document.querySelectorAll('.table thead').forEach(thead => {
                thead.style.background = 'linear-gradient(135deg, var(--theme-primary) 0%, var(--theme-primary-light) 100%)';
            });
        } else {
            document.querySelectorAll('.table thead').forEach(thead => {
                thead.style.background = 'var(--theme-primary)';
            });
        }
    }

    // 应用响应式设置
    applyResponsiveSettings(body) {
        if (this.settings.mobileOptimized) {
            body.classList.add('mobile-optimized');
        } else {
            body.classList.remove('mobile-optimized');
        }
        
        if (this.settings.autoScale) {
            body.classList.add('auto-scale');
        } else {
            body.classList.remove('auto-scale');
        }
    }

    // 初始化页面元素
    initializePageElements() {
        // 自动应用统一样式到现有表格
        document.querySelectorAll('.table').forEach(table => {
            if (!table.classList.contains('table-compact') && 
                !table.classList.contains('table-spacious')) {
                table.classList.add('table-unified');
            }
        });
        
        // 自动应用统一样式到卡片
        document.querySelectorAll('.card').forEach(card => {
            card.classList.add('unified-card');
        });
        
        // 自动应用统一样式到按钮组
        document.querySelectorAll('.btn-group').forEach(btnGroup => {
            if (!btnGroup.classList.contains('btn-group-compact')) {
                btnGroup.classList.add('btn-group-unified');
            }
        });
    }

    // 创建控制面板
    createControlPanel() {
        // 检查是否已存在控制面板
        if (document.getElementById('unified-ui-panel')) return;

        const panel = document.createElement('div');
        panel.id = 'unified-ui-panel';
        panel.className = 'unified-ui-panel';
        panel.innerHTML = `
            <div class="unified-ui-header">
                <h6><i class="fas fa-cogs"></i> 界面统一控制</h6>
                <button class="btn btn-sm btn-outline-secondary" id="toggle-unified-panel">
                    <i class="fas fa-cog"></i>
                </button>
            </div>
            <div class="unified-ui-content" id="unified-ui-content">
                <div class="control-section">
                    <h6>字体设置</h6>
                    <div class="form-group">
                        <label>字体大小</label>
                        <select class="form-control form-control-sm" id="font-size-select">
                            <option value="xs">极小 (0.8x)</option>
                            <option value="sm">小 (0.9x)</option>
                            <option value="normal" selected>正常 (1.0x)</option>
                            <option value="lg">大 (1.1x)</option>
                            <option value="xl">极大 (1.2x)</option>
                        </select>
                    </div>
                </div>
                
                <div class="control-section">
                    <h6>表格设置</h6>
                    <div class="form-group">
                        <label>表格样式</label>
                        <select class="form-control form-control-sm" id="table-style-select">
                            <option value="unified" selected>统一样式</option>
                            <option value="compact">紧凑样式</option>
                            <option value="spacious">宽松样式</option>
                            <option value="minimal">极简样式</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>表格密度</label>
                        <select class="form-control form-control-sm" id="table-density-select">
                            <option value="compact">紧凑</option>
                            <option value="normal" selected>正常</option>
                            <option value="spacious">宽松</option>
                        </select>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="show-table-borders" checked>
                        <label class="form-check-label" for="show-table-borders">显示表格边框</label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="show-table-hover" checked>
                        <label class="form-check-label" for="show-table-hover">悬停高亮</label>
                    </div>
                </div>
                
                <div class="control-section">
                    <h6>间距设置</h6>
                    <div class="form-group">
                        <label>整体间距</label>
                        <select class="form-control form-control-sm" id="spacing-select">
                            <option value="compact">紧凑</option>
                            <option value="normal" selected>正常</option>
                            <option value="spacious">宽松</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>卡片内边距</label>
                        <select class="form-control form-control-sm" id="card-padding-select">
                            <option value="sm">小</option>
                            <option value="normal" selected>正常</option>
                            <option value="lg">大</option>
                        </select>
                    </div>
                </div>
                
                <div class="control-section">
                    <h6>背景设置</h6>
                    <div class="form-group">
                        <label>背景样式</label>
                        <select class="form-control form-control-sm" id="background-style-select">
                            <option value="clean" selected>清洁</option>
                            <option value="textured">纹理</option>
                            <option value="gradient">渐变</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>卡片样式</label>
                        <select class="form-control form-control-sm" id="card-style-select">
                            <option value="flat">平面</option>
                            <option value="elevated" selected>阴影</option>
                            <option value="outlined">边框</option>
                        </select>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="use-gradient-headers" checked>
                        <label class="form-check-label" for="use-gradient-headers">渐变表头</label>
                    </div>
                </div>
                
                <div class="unified-ui-actions">
                    <button class="btn btn-sm btn-primary" id="apply-unified-settings">
                        <i class="fas fa-check"></i> 应用设置
                    </button>
                    <button class="btn btn-sm btn-secondary" id="reset-unified-settings">
                        <i class="fas fa-undo"></i> 重置
                    </button>
                </div>
            </div>
        `;

        // 添加样式
        const style = document.createElement('style');
        style.textContent = `
            .unified-ui-panel {
                position: fixed;
                top: 80px;
                right: 20px;
                width: 320px;
                background: var(--theme-surface, #ffffff);
                border: 1px solid var(--theme-border, #e5e7eb);
                border-radius: 12px;
                box-shadow: 0 10px 30px rgba(0,0,0,0.15);
                z-index: 9998;
                font-size: 14px;
                max-height: 80vh;
                overflow-y: auto;
            }
            
            .unified-ui-header {
                padding: 12px 16px;
                border-bottom: 1px solid var(--theme-border, #e5e7eb);
                display: flex;
                justify-content: space-between;
                align-items: center;
                background: var(--theme-primary, #007bff);
                color: white;
                border-radius: 12px 12px 0 0;
            }
            
            .unified-ui-header h6 {
                margin: 0;
                font-size: 14px;
                font-weight: 600;
            }
            
            .unified-ui-content {
                padding: 16px;
                max-height: 60vh;
                overflow-y: auto;
            }
            
            .unified-ui-content.collapsed {
                display: none;
            }
            
            .control-section {
                margin-bottom: 20px;
                padding-bottom: 16px;
                border-bottom: 1px solid var(--theme-border, #e5e7eb);
            }
            
            .control-section:last-of-type {
                border-bottom: none;
                margin-bottom: 16px;
            }
            
            .control-section h6 {
                font-size: 13px;
                font-weight: 600;
                margin-bottom: 12px;
                color: var(--theme-primary, #007bff);
            }
            
            .unified-ui-actions {
                display: flex;
                gap: 8px;
            }
            
            .unified-ui-actions .btn {
                flex: 1;
            }
            
            @media (max-width: 768px) {
                .unified-ui-panel {
                    width: 300px;
                    right: 10px;
                    top: 70px;
                }
            }
        `;
        document.head.appendChild(style);
        document.body.appendChild(panel);
    }

    // 绑定事件
    bindEvents() {
        // 切换面板显示/隐藏
        const toggleBtn = document.getElementById('toggle-unified-panel');
        const content = document.getElementById('unified-ui-content');
        
        if (toggleBtn && content) {
            toggleBtn.addEventListener('click', () => {
                content.classList.toggle('collapsed');
                const icon = toggleBtn.querySelector('i');
                icon.className = content.classList.contains('collapsed') ? 
                    'fas fa-cog' : 'fas fa-times';
            });
        }

        // 应用设置按钮
        const applyBtn = document.getElementById('apply-unified-settings');
        if (applyBtn) {
            applyBtn.addEventListener('click', () => {
                this.updateSettingsFromForm();
                this.applySettings();
                this.saveSettings();
                this.showNotification('界面设置已应用', 'success');
            });
        }

        // 重置设置按钮
        const resetBtn = document.getElementById('reset-unified-settings');
        if (resetBtn) {
            resetBtn.addEventListener('click', () => {
                this.resetSettings();
                this.updateFormFromSettings();
                this.applySettings();
                this.showNotification('设置已重置', 'info');
            });
        }

        // 实时预览
        const controls = [
            'font-size-select', 'table-style-select', 'table-density-select',
            'spacing-select', 'card-padding-select', 'background-style-select',
            'card-style-select', 'show-table-borders', 'show-table-hover',
            'use-gradient-headers'
        ];
        
        controls.forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.addEventListener('change', () => {
                    this.updateSettingsFromForm();
                    this.applySettings();
                });
            }
        });
    }

    // 从表单更新设置
    updateSettingsFromForm() {
        this.settings.fontSize = document.getElementById('font-size-select')?.value || 'normal';
        this.settings.tableStyle = document.getElementById('table-style-select')?.value || 'unified';
        this.settings.tableDensity = document.getElementById('table-density-select')?.value || 'normal';
        this.settings.spacing = document.getElementById('spacing-select')?.value || 'normal';
        this.settings.cardPadding = document.getElementById('card-padding-select')?.value || 'normal';
        this.settings.backgroundStyle = document.getElementById('background-style-select')?.value || 'clean';
        this.settings.cardStyle = document.getElementById('card-style-select')?.value || 'elevated';
        this.settings.showTableBorders = document.getElementById('show-table-borders')?.checked || false;
        this.settings.showTableHover = document.getElementById('show-table-hover')?.checked || false;
        this.settings.useGradientHeaders = document.getElementById('use-gradient-headers')?.checked || false;
    }

    // 从设置更新表单
    updateFormFromSettings() {
        const elements = {
            'font-size-select': this.settings.fontSize,
            'table-style-select': this.settings.tableStyle,
            'table-density-select': this.settings.tableDensity,
            'spacing-select': this.settings.spacing,
            'card-padding-select': this.settings.cardPadding,
            'background-style-select': this.settings.backgroundStyle,
            'card-style-select': this.settings.cardStyle
        };

        Object.entries(elements).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) element.value = value;
        });

        const checkboxes = {
            'show-table-borders': this.settings.showTableBorders,
            'show-table-hover': this.settings.showTableHover,
            'use-gradient-headers': this.settings.useGradientHeaders
        };

        Object.entries(checkboxes).forEach(([id, checked]) => {
            const element = document.getElementById(id);
            if (element) element.checked = checked;
        });
    }

    // 重置设置
    resetSettings() {
        this.settings = {
            fontSize: 'normal',
            fontScale: 1.0,
            tableStyle: 'unified',
            tableDensity: 'normal',
            showTableBorders: true,
            showTableHover: true,
            spacing: 'normal',
            cardPadding: 'normal',
            backgroundStyle: 'clean',
            cardStyle: 'elevated',
            useGradientHeaders: true,
            headerStyle: 'gradient',
            mobileOptimized: true,
            autoScale: true
        };
    }

    // 显示通知
    showNotification(message, type = 'info') {
        // 如果有toastr，使用toastr
        if (window.toastr) {
            window.toastr[type](message);
            return;
        }

        // 否则使用简单的通知
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} alert-dismissible fade show`;
        notification.style.cssText = `
            position: fixed;
            top: 80px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 10000;
            min-width: 300px;
            text-align: center;
        `;
        notification.innerHTML = `
            ${message}
            <button type="button" class="close" data-dismiss="alert">
                <span>&times;</span>
            </button>
        `;
        
        document.body.appendChild(notification);
        
        // 3秒后自动移除
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 3000);
    }

    // 公共方法：应用采购订单页面样式
    applyPurchaseOrderStyle() {
        this.settings = {
            fontSize: 'normal',
            tableStyle: 'unified',
            tableDensity: 'normal',
            spacing: 'normal',
            cardPadding: 'normal',
            backgroundStyle: 'clean',
            cardStyle: 'elevated',
            showTableBorders: false,
            showTableHover: true,
            useGradientHeaders: true,
            mobileOptimized: true,
            autoScale: true
        };
        this.applySettings();
        this.saveSettings();
        this.updateFormFromSettings();
    }
}

// 初始化统一界面控制器
document.addEventListener('DOMContentLoaded', function() {
    window.unifiedUIController = new UnifiedUIController();
    
    // 添加全局快捷键
    document.addEventListener('keydown', function(e) {
        // Ctrl+Shift+U 快速切换界面控制面板
        if (e.ctrlKey && e.shiftKey && e.key === 'U') {
            e.preventDefault();
            const panel = document.getElementById('unified-ui-panel');
            if (panel) {
                panel.style.display = panel.style.display === 'none' ? 'block' : 'none';
            }
        }
    });
});

// 导出给其他脚本使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = UnifiedUIController;
}
