#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
引导场景管理功能验证脚本
验证数据库数据和服务功能
"""

import sys
import os
import requests
import json

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def verify_database_data():
    """验证数据库中的场景数据"""
    try:
        from flask import Flask
        from flask_sqlalchemy import SQLAlchemy
        from config import Config
        from sqlalchemy import text
        
        app = Flask(__name__)
        app.config.from_object(Config)
        db = SQLAlchemy(app)
        
        with app.app_context():
            print("🔍 验证数据库中的场景数据:")
            print("-" * 40)
            
            # 查询场景配置
            scenarios = db.session.execute(text("""
                SELECT scenario_type, name, theme_color, simplified_mode, 
                       advanced_features, usage_count, is_active
                FROM guide_scenarios 
                ORDER BY scenario_type
            """)).fetchall()
            
            print(f"✅ 找到 {len(scenarios)} 个场景配置:")
            for scenario in scenarios:
                status = "✅ 活跃" if scenario[6] else "❌ 禁用"
                mode = []
                if scenario[3]:  # simplified_mode
                    mode.append("简化")
                if scenario[4]:  # advanced_features
                    mode.append("高级")
                mode_str = f"({', '.join(mode)})" if mode else ""
                
                print(f"  - {scenario[0]}: {scenario[1]} {mode_str}")
                print(f"    主题: {scenario[2]}, 使用次数: {scenario[5]}, 状态: {status}")
            
            # 查询使用记录
            usage_count = db.session.execute(text(
                "SELECT COUNT(*) FROM guide_scenario_usage"
            )).fetchone()[0]
            
            feedback_count = db.session.execute(text(
                "SELECT COUNT(*) FROM guide_scenario_feedback"
            )).fetchone()[0]
            
            print(f"\n📊 统计信息:")
            print(f"  - 使用记录: {usage_count} 条")
            print(f"  - 反馈记录: {feedback_count} 条")
            
            return True
            
    except Exception as e:
        print(f"❌ 数据库验证失败: {str(e)}")
        return False

def verify_scenario_service():
    """验证场景服务功能"""
    try:
        from flask import Flask
        from flask_sqlalchemy import SQLAlchemy
        from config import Config
        from app.services.scenario_guide_service import ScenarioGuideService
        
        app = Flask(__name__)
        app.config.from_object(Config)
        db = SQLAlchemy(app)
        
        with app.app_context():
            print("\n🎯 验证场景服务功能:")
            print("-" * 40)
            
            # 测试从数据库加载场景
            print("1. 从数据库加载场景配置...")
            success = ScenarioGuideService.load_scenarios_from_database()
            print(f"   {'✅ 成功' if success else '❌ 失败'}")
            
            # 测试场景类型
            print(f"\n2. 可用场景类型 ({len(ScenarioGuideService.SCHOOL_TYPES)} 个):")
            for scenario_type, info in ScenarioGuideService.SCHOOL_TYPES.items():
                print(f"   - {scenario_type}: {info['name']}")
            
            # 测试智能检测
            print(f"\n3. 智能场景检测测试:")
            test_schools = [
                "北京市朝阳区实验小学",
                "清华大学",
                "山村希望小学",
                "北京职业技术学院",
                "育才中学"
            ]
            
            for school_name in test_schools:
                detected = ScenarioGuideService.detect_school_type(school_name)
                scenario_name = ScenarioGuideService.SCHOOL_TYPES.get(detected, {}).get('name', '未知')
                print(f"   {school_name} -> {detected} ({scenario_name})")
            
            # 测试个性化引导计划
            print(f"\n4. 个性化引导计划测试:")
            test_scenarios = ['primary', 'university', 'rural']
            for scenario_type in test_scenarios:
                plan = ScenarioGuideService.create_personalized_guide_plan(scenario_type)
                scenario_name = ScenarioGuideService.SCHOOL_TYPES.get(scenario_type, {}).get('name', scenario_type)
                print(f"   {scenario_name}: {len(plan['steps'])} 个步骤, 预计 {plan['estimated_time']}")
            
            # 测试场景报告
            print(f"\n5. 场景适用性报告:")
            for scenario_type in ['primary', 'university']:
                report = ScenarioGuideService.generate_scenario_report(scenario_type)
                scenario_name = ScenarioGuideService.SCHOOL_TYPES.get(scenario_type, {}).get('name', scenario_type)
                print(f"   {scenario_name}: 适用性评分 {report['suitability_score']}/10")
                print(f"     推荐理由: {', '.join(report['recommendations'][:2])}")
            
            return True
            
    except Exception as e:
        print(f"❌ 场景服务验证失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def verify_api_endpoints():
    """验证API端点（需要应用运行）"""
    try:
        print("\n🌐 验证API端点:")
        print("-" * 40)
        
        base_url = "http://127.0.0.1:5000"
        
        # 测试主页是否可访问
        print("1. 测试主页访问...")
        try:
            response = requests.get(f"{base_url}/", timeout=5)
            print(f"   主页状态: {response.status_code}")
        except requests.exceptions.RequestException as e:
            print(f"   ❌ 主页访问失败: {str(e)}")
            return False
        
        # 测试场景管理页面（需要登录）
        print("2. 测试场景管理页面...")
        try:
            response = requests.get(f"{base_url}/admin/guide/scenarios", timeout=5)
            if response.status_code == 302:  # 重定向到登录页面
                print("   ✅ 场景管理页面存在（需要登录）")
            elif response.status_code == 200:
                print("   ✅ 场景管理页面可访问")
            else:
                print(f"   ⚠️  场景管理页面状态: {response.status_code}")
        except requests.exceptions.RequestException as e:
            print(f"   ❌ 场景管理页面访问失败: {str(e)}")
        
        # 测试场景分析页面
        print("3. 测试场景分析页面...")
        try:
            response = requests.get(f"{base_url}/admin/guide/analytics", timeout=5)
            if response.status_code in [200, 302]:
                print("   ✅ 场景分析页面存在")
            else:
                print(f"   ⚠️  场景分析页面状态: {response.status_code}")
        except requests.exceptions.RequestException as e:
            print(f"   ❌ 场景分析页面访问失败: {str(e)}")
        
        return True
        
    except Exception as e:
        print(f"❌ API端点验证失败: {str(e)}")
        return False

def verify_scenario_creation():
    """验证场景创建功能"""
    try:
        from flask import Flask
        from flask_sqlalchemy import SQLAlchemy
        from config import Config
        from app.services.scenario_guide_service import ScenarioGuideService
        from app.models.guide_scenario import GuideScenario
        
        app = Flask(__name__)
        app.config.from_object(Config)
        db = SQLAlchemy(app)
        
        with app.app_context():
            print("\n🆕 验证场景创建功能:")
            print("-" * 40)
            
            # 创建测试场景
            test_scenario = {
                'scenario_type': 'test_verification',
                'name': '验证测试场景',
                'description': '这是一个用于验证功能的测试场景',
                'characteristics': ['测试特征1', '测试特征2', '验证功能'],
                'focus_areas': ['功能测试', '数据验证', '系统稳定性'],
                'welcome_message': '欢迎使用验证测试场景！这个场景用于验证系统功能。',
                'priority_steps': ['daily_management', 'suppliers', 'weekly_menu'],
                'simplified_mode': False,
                'advanced_features': True
            }
            
            print("1. 创建测试场景...")
            success = ScenarioGuideService.create_new_scenario(test_scenario)
            print(f"   {'✅ 创建成功' if success else '❌ 创建失败'}")
            
            if success:
                # 验证场景是否已保存
                print("2. 验证场景是否已保存到数据库...")
                scenario = GuideScenario.query.filter_by(scenario_type='test_verification').first()
                
                if scenario:
                    print(f"   ✅ 场景已保存: {scenario.name}")
                    print(f"   场景ID: {scenario.id}")
                    print(f"   特征数量: {len(scenario.to_dict()['characteristics'])}")
                    print(f"   关注点数量: {len(scenario.to_dict()['focus_areas'])}")
                    print(f"   优先步骤数量: {len(scenario.to_dict()['priority_steps'])}")
                    
                    # 测试场景配置
                    print("3. 测试场景配置...")
                    test_result = ScenarioGuideService.test_scenario_config('test_verification', user_id=1)
                    if test_result.get('validation_passed', True):
                        print("   ✅ 场景配置验证通过")
                    else:
                        print(f"   ⚠️  场景配置有问题: {', '.join(test_result.get('issues', []))}")
                    
                    # 清理测试数据
                    print("4. 清理测试数据...")
                    db.session.delete(scenario)
                    db.session.commit()
                    print("   ✅ 测试数据已清理")
                    
                    return True
                else:
                    print("   ❌ 场景未找到")
                    return False
            
            return success
            
    except Exception as e:
        print(f"❌ 场景创建验证失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主验证函数"""
    print("=" * 60)
    print("引导场景管理功能验证")
    print("=" * 60)
    
    verifications = [
        ("数据库数据验证", verify_database_data),
        ("场景服务验证", verify_scenario_service),
        ("API端点验证", verify_api_endpoints),
        ("场景创建验证", verify_scenario_creation)
    ]
    
    results = []
    
    for verification_name, verification_func in verifications:
        print(f"\n🧪 {verification_name}")
        print("=" * 60)
        try:
            result = verification_func()
            results.append((verification_name, result))
        except Exception as e:
            print(f"❌ {verification_name}执行失败: {str(e)}")
            results.append((verification_name, False))
    
    # 输出验证总结
    print("\n" + "=" * 60)
    print("验证结果总结")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for verification_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{verification_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个验证通过")
    
    if passed == total:
        print("\n🎉 所有验证通过！引导场景管理功能完全就绪。")
        print("\n📝 功能使用指南:")
        print("1. 访问 http://127.0.0.1:5000/admin/guide/scenarios 管理场景")
        print("2. 访问 http://127.0.0.1:5000/admin/guide/analytics 查看分析")
        print("3. 使用 ScenarioGuideService 进行编程操作")
        print("4. 运行 python demo_guide_scenario.py 查看完整演示")
        
        print("\n🚀 系统特性:")
        print("✓ 6种预定义学校场景（小学、中学、高中、职业学校、大学、乡村学校）")
        print("✓ 智能场景检测和推荐")
        print("✓ 个性化引导流程生成")
        print("✓ 可视化场景管理界面")
        print("✓ 详细的使用分析和反馈收集")
        print("✓ 完整的数据库持久化存储")
    else:
        print(f"\n⚠️  有 {total - passed} 个验证失败，请检查相关配置。")
        
        if passed > 0:
            print("\n✅ 部分功能正常，系统基本可用。")
    
    return passed == total

if __name__ == '__main__':
    main()
