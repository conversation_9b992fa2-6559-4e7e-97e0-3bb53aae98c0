# Models package initialization
# This file makes the models directory a Python package

# Import all models from the main models.py file to maintain backward compatibility
# We need to import from the parent directory's models.py file

# Import the main models from the parent models.py file
import importlib.util
import os

# Get the path to the parent models.py file
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
models_file = os.path.join(parent_dir, 'models.py')

# Load the models module
spec = importlib.util.spec_from_file_location("models", models_file)
models_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(models_module)

# Import all the models from the main models.py file
# Only import models that actually exist
User = models_module.User
Role = models_module.Role
UserRole = models_module.UserRole
AdministrativeArea = models_module.AdministrativeArea
Supplier = models_module.Supplier
SupplierCategory = models_module.SupplierCategory
SupplierProduct = models_module.SupplierProduct
ProductSpecParameter = models_module.ProductSpecParameter
Ingredient = models_module.Ingredient
IngredientCategory = models_module.IngredientCategory
IngredientInspection = models_module.IngredientInspection
Recipe = models_module.Recipe
RecipeCategory = models_module.RecipeCategory
RecipeIngredient = models_module.RecipeIngredient
WeeklyMenu = models_module.WeeklyMenu
WeeklyMenuRecipe = models_module.WeeklyMenuRecipe
Warehouse = models_module.Warehouse
StorageLocation = models_module.StorageLocation
StockIn = models_module.StockIn
StockInItem = models_module.StockInItem  # Correct name
StockInDocument = models_module.StockInDocument
StockOut = models_module.StockOut
StockOutItem = models_module.StockOutItem  # Correct name
Inventory = models_module.Inventory
FoodSample = models_module.FoodSample
ConsumptionPlan = models_module.ConsumptionPlan
ConsumptionDetail = models_module.ConsumptionDetail
InventoryAlert = models_module.InventoryAlert
StandardModel = models_module.StandardModel
Notification = models_module.Notification
SupplierCertificate = models_module.SupplierCertificate
SupplierDelivery = models_module.SupplierDelivery
SupplierSchoolRelation = models_module.SupplierSchoolRelation
PurchaseOrder = models_module.PurchaseOrder
PurchaseOrderItem = models_module.PurchaseOrderItem
PurchaseRequisition = models_module.PurchaseRequisition
PurchaseRequisitionItem = models_module.PurchaseRequisitionItem

# Import other available models
Employee = models_module.Employee
DailyHealthCheck = models_module.DailyHealthCheck
TrainingRecord = models_module.TrainingRecord
MedicalExamination = models_module.MedicalExamination
HealthCertificate = models_module.HealthCertificate
DeliveryInspection = models_module.DeliveryInspection
DeliveryItem = models_module.DeliveryItem
InventoryCheck = models_module.InventoryCheck
InventoryCheckItem = models_module.InventoryCheckItem
MenuPlan = models_module.MenuPlan
MenuRecipe = models_module.MenuRecipe
RecipeProcess = models_module.RecipeProcess
RecipeProcessIngredient = models_module.RecipeProcessIngredient
AuditLog = models_module.AuditLog
AreaChangeHistory = models_module.AreaChangeHistory

# Import guide scenario models from the models subdirectory
try:
    from .guide_scenario import GuideScenario, GuideScenarioUsage, GuideScenarioFeedback
except ImportError:
    pass
