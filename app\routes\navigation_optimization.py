"""
导航菜单优化路由
提供优化后的菜单配置和切换功能
"""

from flask import Blueprint, render_template, request, jsonify, session, redirect, url_for
from flask_login import login_required, current_user
from datetime import datetime
from app.utils.optimized_menu import get_optimized_menu, get_menu_statistics, OPTIMIZED_MENU_CONFIG, MOBILE_MENU_CONFIG

bp = Blueprint('navigation_optimization', __name__, url_prefix='/navigation')

@bp.route('/')
@login_required
def index():
    """导航优化主页"""
    return render_template('navigation_optimization/index.html')

@bp.route('/demo')
def demo():
    """导航优化演示页面（无需登录）"""
    return render_template('navigation_optimization/demo.html')

@bp.route('/api/menu')
@login_required
def get_menu():
    """获取优化后的菜单配置"""
    mobile = request.args.get('mobile', 'false').lower() == 'true'
    menu = get_optimized_menu(current_user, mobile=mobile)
    return jsonify({
        'success': True,
        'menu': menu,
        'mode': 'mobile' if mobile else 'desktop'
    })

@bp.route('/api/statistics')
@login_required
def get_statistics():
    """获取菜单优化统计信息"""
    stats = get_menu_statistics()
    return jsonify({
        'success': True,
        'statistics': stats
    })

@bp.route('/api/toggle-mode', methods=['POST'])
@login_required
def toggle_mode():
    """切换导航模式"""
    data = request.get_json()
    mode = data.get('mode', 'auto')

    # 保存用户偏好
    session['navigation_mode'] = mode

    return jsonify({
        'success': True,
        'mode': mode,
        'message': f'导航模式已切换为: {mode}'
    })

@bp.route('/api/usage-stats', methods=['POST'])
@login_required
def update_usage_stats():
    """更新菜单使用统计"""
    data = request.get_json()
    menu_id = data.get('menu_id')

    if not menu_id:
        return jsonify({'success': False, 'message': '缺少菜单ID'})

    # 获取当前用户的使用统计
    usage_key = f'menu_usage_{current_user.id}'
    usage_stats = session.get(usage_key, {})

    # 更新使用次数
    usage_stats[menu_id] = usage_stats.get(menu_id, 0) + 1
    session[usage_key] = usage_stats

    return jsonify({
        'success': True,
        'usage_count': usage_stats[menu_id]
    })

@bp.route('/api/reset-usage', methods=['POST'])
@login_required
def reset_usage():
    """重置菜单使用统计"""
    usage_key = f'menu_usage_{current_user.id}'
    session.pop(usage_key, None)

    return jsonify({
        'success': True,
        'message': '使用统计已重置'
    })

@bp.route('/switch-to-optimized')
@login_required
def switch_to_optimized():
    """切换到优化后的导航菜单"""
    session['use_optimized_menu'] = True
    return redirect(url_for('main.index'))

@bp.route('/switch-to-original')
@login_required
def switch_to_original():
    """切换回原始导航菜单"""
    session['use_optimized_menu'] = False
    return redirect(url_for('main.index'))

@bp.route('/compare')
@login_required
def compare():
    """菜单对比页面"""
    from app.utils.menu import MENU_CONFIG

    # 获取原始菜单和优化菜单
    original_menu = MENU_CONFIG
    optimized_menu = OPTIMIZED_MENU_CONFIG

    # 统计信息
    stats = get_menu_statistics()

    return render_template('navigation_optimization/compare.html',
                         original_menu=original_menu,
                         optimized_menu=optimized_menu,
                         stats=stats)

@bp.route('/settings')
@login_required
def settings():
    """导航设置页面"""
    current_mode = session.get('navigation_mode', 'auto')
    use_optimized = session.get('use_optimized_menu', False)

    return render_template('navigation_optimization/settings.html',
                         current_mode=current_mode,
                         use_optimized=use_optimized)

@bp.route('/api/export-config')
@login_required
def export_config():
    """导出导航配置"""
    config = {
        'navigation_mode': session.get('navigation_mode', 'auto'),
        'use_optimized_menu': session.get('use_optimized_menu', False),
        'menu_usage': session.get(f'menu_usage_{current_user.id}', {}),
        'user_id': current_user.id,
        'export_time': str(datetime.now())
    }

    return jsonify({
        'success': True,
        'config': config,
        'filename': f'navigation_config_{current_user.id}.json'
    })

@bp.route('/api/import-config', methods=['POST'])
@login_required
def import_config():
    """导入导航配置"""
    data = request.get_json()
    config = data.get('config', {})

    if not config:
        return jsonify({'success': False, 'message': '配置数据为空'})

    try:
        # 导入设置
        if 'navigation_mode' in config:
            session['navigation_mode'] = config['navigation_mode']

        if 'use_optimized_menu' in config:
            session['use_optimized_menu'] = config['use_optimized_menu']

        if 'menu_usage' in config:
            usage_key = f'menu_usage_{current_user.id}'
            session[usage_key] = config['menu_usage']

        return jsonify({
            'success': True,
            'message': '配置导入成功'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'配置导入失败: {str(e)}'
        })

# 注册蓝图时的辅助函数
def register_navigation_context():
    """注册导航相关的模板上下文"""
    from flask import current_app

    @current_app.context_processor
    def inject_navigation_vars():
        """注入导航相关变量到模板"""
        use_optimized = session.get('use_optimized_menu', False)
        navigation_mode = session.get('navigation_mode', 'auto')

        # 如果使用优化菜单，获取优化后的菜单
        if use_optimized and current_user.is_authenticated:
            user_menu = get_optimized_menu(current_user)
        else:
            # 使用原始菜单逻辑
            from app.utils.menu import get_user_menu
            user_menu = get_user_menu(current_user) if current_user.is_authenticated else []

        return {
            'use_optimized_menu': use_optimized,
            'navigation_mode': navigation_mode,
            'user_menu': user_menu,
            'menu_stats': get_menu_statistics() if current_user.is_authenticated else None
        }

# 在应用启动时调用
def init_navigation_optimization(app):
    """初始化导航优化功能"""
    app.register_blueprint(bp)

    # 注册模板上下文
    with app.app_context():
        register_navigation_context()

    print("✅ 导航菜单优化功能已初始化")
