/* 取消所有模糊效果 - 保持界面清晰 */

/* === 全局禁用模糊效果 === */

/* 1. 禁用所有 backdrop-filter 模糊效果 */
*,
*::before,
*::after {
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
}

/* 2. 禁用所有 filter 模糊效果 */
*,
*::before,
*::after {
    filter: none !important;
    -webkit-filter: none !important;
}

/* 3. 重置所有可能的模糊类 */
.backdrop-blur,
.backdrop-blur-sm,
.backdrop-blur-md,
.backdrop-blur-lg,
.backdrop-blur-xl,
.backdrop-blur-2xl,
.backdrop-blur-3xl {
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
}

.blur,
.blur-sm,
.blur-md,
.blur-lg,
.blur-xl,
.blur-2xl,
.blur-3xl {
    filter: none !important;
    -webkit-filter: none !important;
}

/* === 主题相关模糊效果禁用 === */

/* 禁用主题切换器中的模糊效果 */
.theme-switcher .dropdown-menu {
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
}

/* 禁用现代科技主题的模糊效果 */
[data-theme="modern"] .dropdown-menu {
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
}

/* 禁用所有主题的玻璃效果 */
[data-theme] * {
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
}

/* === 组件特定模糊效果禁用 === */

/* 1. 导航栏 */
.navbar,
.navbar-nav,
.nav-link {
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
    filter: none !important;
    -webkit-filter: none !important;
}

/* 2. 下拉菜单 */
.dropdown-menu,
.dropdown-item {
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
    filter: none !important;
    -webkit-filter: none !important;
}

/* 3. 模态框 */
.modal,
.modal-dialog,
.modal-content,
.modal-backdrop {
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
    filter: none !important;
    -webkit-filter: none !important;
}

/* 4. 卡片 */
.card,
.card-header,
.card-body,
.card-footer {
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
    filter: none !important;
    -webkit-filter: none !important;
}

/* 5. 按钮 */
.btn,
.btn-primary,
.btn-secondary,
.btn-success,
.btn-warning,
.btn-info,
.btn-danger,
.btn-dark,
.btn-light {
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
    filter: none !important;
    -webkit-filter: none !important;
}

/* 6. 表单控件 */
.form-control,
.form-select,
.form-check-input,
.input-group {
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
    filter: none !important;
    -webkit-filter: none !important;
}

/* 7. 警告框 */
.alert,
.alert-primary,
.alert-secondary,
.alert-success,
.alert-warning,
.alert-info,
.alert-danger,
.alert-dark,
.alert-light {
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
    filter: none !important;
    -webkit-filter: none !important;
}

/* 8. 徽章 */
.badge,
.badge-primary,
.badge-secondary,
.badge-success,
.badge-warning,
.badge-info,
.badge-danger,
.badge-dark,
.badge-light {
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
    filter: none !important;
    -webkit-filter: none !important;
}

/* 9. 进度条 */
.progress,
.progress-bar {
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
    filter: none !important;
    -webkit-filter: none !important;
}

/* 10. 表格 */
.table,
.table-responsive,
.table thead,
.table tbody,
.table tfoot {
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
    filter: none !important;
    -webkit-filter: none !important;
}

/* === 特殊效果禁用 === */

/* 禁用文字阴影（可能造成模糊感） */
*,
*::before,
*::after {
    text-shadow: none !important;
}

/* 禁用图片模糊效果 */
img,
.img-fluid,
.img-thumbnail {
    filter: none !important;
    -webkit-filter: none !important;
}

/* === Tailwind CSS 模糊类禁用 === */

/* 禁用 Tailwind 的模糊工具类 */
.filter,
.backdrop-filter {
    filter: none !important;
    backdrop-filter: none !important;
}

/* === 自定义组件模糊效果禁用 === */

/* 禁用主题切换器的特殊效果 */
.theme-switcher * {
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
    filter: none !important;
    -webkit-filter: none !important;
}

/* 禁用图片上传器的模糊效果 */
.image-uploader *,
.enhanced-image-uploader * {
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
    filter: none !important;
    -webkit-filter: none !important;
}

/* === 覆盖主题变量中的模糊效果 === */

/* 重置主题变量中的模糊效果 */
:root,
[data-theme] {
    --glass-effect: none !important;
    --backdrop-blur: none !important;
    --filter-blur: none !important;
}

/* === 确保文字清晰度 === */

/* 优化文字渲染，确保清晰 */
body,
html {
    -webkit-font-smoothing: antialiased !important;
    -moz-osx-font-smoothing: grayscale !important;
    text-rendering: optimizeLegibility !important;
}

/* 确保所有文字元素清晰 */
h1, h2, h3, h4, h5, h6,
p, span, div, a, button,
.card-title, .navbar-brand,
.btn, .form-control {
    -webkit-font-smoothing: antialiased !important;
    -moz-osx-font-smoothing: grayscale !important;
    text-rendering: optimizeLegibility !important;
}

/* === 移动端优化 === */

/* 移动端禁用所有可能的模糊效果 */
@media (max-width: 768px) {
    * {
        backdrop-filter: none !important;
        -webkit-backdrop-filter: none !important;
        filter: none !important;
        -webkit-filter: none !important;
        text-shadow: none !important;
    }
}

/* === 打印样式优化 === */

/* 打印时确保没有模糊效果 */
@media print {
    * {
        backdrop-filter: none !important;
        -webkit-backdrop-filter: none !important;
        filter: none !important;
        -webkit-filter: none !important;
        text-shadow: none !important;
        box-shadow: none !important;
    }
}
