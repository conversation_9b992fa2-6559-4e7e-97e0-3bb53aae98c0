/**
 * 主题切换系统
 * 支持动态切换主题颜色，保存用户偏好，实时预览效果
 */

class ThemeSwitcher {
    constructor() {
        this.currentTheme = 'primary';
        // 专业主题颜色系统 - 基于色彩心理学和设计原理
        this.themes = {
            // 专业商务系列
            'primary': {
                name: '🌊 海洋蓝主题',
                color: '#001F3F',
                description: '专业、信任、稳定',
                category: 'professional',
                accent: '#FF6B6B',
                surface: '#F8F9FA'
            },
            'secondary': {
                name: '🔘 现代灰主题',
                color: '#475569',
                description: '简约、专业、平衡',
                category: 'professional',
                accent: '#F7FAFC',
                surface: '#F7FAFC'
            },

            // 科技现代系列
            'modern': {
                name: '🚀 现代科技主题',
                color: '#1E1E2E',
                description: '极简、未来感、科技',
                category: 'tech',
                accent: '#00F5D4',
                surface: '#1A1A2E'
            },
            'dark': {
                name: '🌙 深色主题',
                color: '#4F46E5',
                description: '护眼、专业、现代',
                category: 'dark',
                accent: '#10B981',
                surface: '#1E293B'
            },

            // 自然健康系列
            'success': {
                name: '🌿 自然绿主题',
                color: '#4A7856',
                description: '健康、成长、和谐',
                category: 'natural',
                accent: '#BC8A5F',
                surface: '#F0FDF4'
            },
            'nature': {
                name: '🍃 生态绿主题',
                color: '#22C55E',
                description: '生态、环保、自然',
                category: 'natural',
                accent: '#F59E0B',
                surface: '#F0FDF4'
            },

            // 活力创新系列
            'warning': {
                name: '🔥 活力橙主题',
                color: '#FF6B35',
                description: '活力、创新、温暖',
                category: 'vibrant',
                accent: '#0353A4',
                surface: '#FEF9C7'
            },
            'energy': {
                name: '⚡ 能量橙主题',
                color: '#F59E0B',
                description: '能量、动感、热情',
                category: 'vibrant',
                accent: '#3B82F6',
                surface: '#FEF9C7'
            },

            // 优雅精致系列
            'info': {
                name: '💜 优雅紫主题',
                color: '#7B5CBF',
                description: '创新、优雅、神秘',
                category: 'elegant',
                accent: '#22C55E',
                surface: '#FAF5FF'
            },
            'elegant': {
                name: '🎨 艺术紫主题',
                color: '#8B5CF6',
                description: '艺术、创意、灵感',
                category: 'elegant',
                accent: '#F59E0B',
                surface: '#FAF5FF'
            },

            // 警示提醒系列
            'danger': {
                name: '❤️ 深邃红主题',
                color: '#C91F37',
                description: '力量、重要、警示',
                category: 'alert',
                accent: '#F87171',
                surface: '#FEF2F2'
            },

            // 中性平衡系列
            'classic-neutral': {
                name: '🏛️ 经典中性风',
                color: '#8B4513',
                description: '稳重、传统、经典',
                category: 'neutral',
                accent: '#DAA520',
                surface: '#FAF0E6'
            },
            'modern-neutral': {
                name: '⚪ 现代中性风',
                color: '#475569',
                description: '简约、平衡、现代',
                category: 'neutral',
                accent: '#F7FAFC',
                surface: '#F7FAFC'
            },

            // 经典优雅系列
            'vintage-elegant': {
                name: '🍷 复古典雅风',
                color: '#004225',
                description: '酒红色·墨绿色·金色，浓郁醇厚',
                category: 'vintage',
                accent: '#8B4513',
                surface: '#F3E5AB'
            },
            'noble-elegant': {
                name: '👑 贵族典雅风',
                color: '#191970',
                description: '藏青色·深紫色·银色，深邃高贵',
                category: 'vintage',
                accent: '#663399',
                surface: '#F8F8FF'
            },
            'fresh-elegant': {
                name: '🌸 清新优雅风',
                color: '#4682B4',
                description: '钢蓝色·热粉色·幽灵白，温馨浪漫',
                category: 'vintage',
                accent: '#FF69B4',
                surface: '#F8F8FF'
            },
            'spring-fresh': {
                name: '🌱 春日清新风',
                color: '#228B22',
                description: '森林绿·金色·薄荷奶油，清爽宜人',
                category: 'vintage',
                accent: '#FFD700',
                surface: '#F0FFF0'
            },
            'luxury-solemn': {
                name: '✨ 华丽庄重风',
                color: '#FFD700',
                description: '金色·黑色·红色，华丽高贵',
                category: 'vintage',
                accent: '#DC143C',
                surface: '#FFFAF0'
            },
            'royal-solemn': {
                name: '🎭 皇室庄重风',
                color: '#4B0082',
                description: '深紫色·深红色·黑色，神秘庄重',
                category: 'vintage',
                accent: '#8B0000',
                surface: '#F8F8FF'
            }
        };

        // 主题分类定义
        this.themeCategories = {
            'professional': { name: '专业商务', icon: '💼', description: '适合正式商务环境' },
            'tech': { name: '科技现代', icon: '🚀', description: '现代科技感设计' },
            'natural': { name: '自然健康', icon: '🌿', description: '健康环保理念' },
            'vibrant': { name: '活力创新', icon: '🔥', description: '充满活力和创新' },
            'elegant': { name: '优雅精致', icon: '💜', description: '优雅精致风格' },
            'alert': { name: '警示提醒', icon: '⚠️', description: '重要提醒场景' },
            'dark': { name: '深色护眼', icon: '🌙', description: '护眼深色模式' },
            'neutral': { name: '中性平衡', icon: '⚪', description: '平衡中性设计' },
            'vintage': { name: '经典优雅', icon: '🎭', description: '经典优雅复古风格' }
        };

        this.init();
    }

    /**
     * 初始化主题系统
     */
    init() {
        // 从系统设置或本地存储加载主题
        this.loadTheme();

        // 应用主题
        this.applyTheme(this.currentTheme);

        // 绑定事件监听器
        this.bindEvents();

        // 创建主题切换器（如果需要）
        this.createThemeSwitcher();
    }

    /**
     * 加载主题设置
     */
    loadTheme() {
        // 优先从系统设置加载
        const systemTheme = this.getSystemTheme();
        if (systemTheme) {
            this.currentTheme = systemTheme;
            return;
        }

        // 其次从本地存储加载
        const savedTheme = localStorage.getItem('user-theme');
        if (savedTheme && this.themes[savedTheme]) {
            this.currentTheme = savedTheme;
            return;
        }

        // 最后使用默认主题
        this.currentTheme = 'primary';
    }

    /**
     * 获取系统设置的主题
     */
    getSystemTheme() {
        // 从页面的 data-theme 属性获取
        const bodyTheme = document.body.getAttribute('data-theme');
        if (bodyTheme && this.themes[bodyTheme]) {
            return bodyTheme;
        }

        // 从页面的 meta 标签获取系统主题设置
        const metaTheme = document.querySelector('meta[name="theme-color"]');
        if (metaTheme) {
            const themeValue = metaTheme.getAttribute('content');
            if (this.themes[themeValue]) {
                return themeValue;
            }
        }

        // 从全局变量获取（如果有的话）
        if (window.systemSettings && window.systemSettings.theme_color) {
            return window.systemSettings.theme_color;
        }

        // 从系统设置页面的选择器获取
        const systemSelector = document.getElementById('setting_theme_color');
        if (systemSelector && systemSelector.value) {
            return systemSelector.value;
        }

        return null;
    }

    /**
     * 应用主题
     */
    applyTheme(themeName) {
        if (!this.themes[themeName]) {
            console.warn(`主题 "${themeName}" 不存在，使用默认主题`);
            themeName = 'primary';
        }

        // 设置 data-theme 属性
        document.documentElement.setAttribute('data-theme', themeName);
        document.body.setAttribute('data-theme', themeName);

        // 更新导航栏背景色
        this.updateNavbarTheme(themeName);

        // 更新当前主题
        this.currentTheme = themeName;

        // 保存到本地存储
        localStorage.setItem('user-theme', themeName);

        // 更新页面标题栏颜色（移动端）
        this.updateMetaThemeColor(this.themes[themeName].color);

        // 触发主题变更事件
        this.dispatchThemeChangeEvent(themeName);

        // 更新主题选择器状态
        this.updateThemeSelectorState(themeName);

        // 保存到服务器（如果用户已登录）
        this.saveThemeToServer(themeName);

        // 追踪主题使用统计
        this.trackThemeUsage(themeName);

        console.log(`主题已切换到: ${this.themes[themeName].name}`);
    }

    /**
     * 更新导航栏主题
     */
    updateNavbarTheme(themeName) {
        const navbar = document.querySelector('.navbar');
        if (!navbar) return;

        // 移除所有可能的背景色类
        const bgClasses = ['bg-primary', 'bg-secondary', 'bg-success', 'bg-warning', 'bg-info', 'bg-danger', 'bg-dark',
                          'bg-modern', 'bg-nature', 'bg-energy', 'bg-elegant',
                          'bg-classic-neutral', 'bg-modern-neutral', 'bg-vintage-elegant', 'bg-noble-elegant',
                          'bg-fresh-elegant', 'bg-spring-fresh', 'bg-luxury-solemn', 'bg-royal-solemn'];

        bgClasses.forEach(cls => navbar.classList.remove(cls));

        // 添加新的背景色类
        navbar.classList.add(`bg-${themeName}`);
    }

    /**
     * 更新 meta theme-color
     */
    updateMetaThemeColor(color) {
        let metaTheme = document.querySelector('meta[name="theme-color"]');
        if (!metaTheme) {
            metaTheme = document.createElement('meta');
            metaTheme.name = 'theme-color';
            document.head.appendChild(metaTheme);
        }
        metaTheme.content = color;
    }

    /**
     * 触发主题变更事件
     */
    dispatchThemeChangeEvent(themeName) {
        const event = new CustomEvent('themeChanged', {
            detail: {
                theme: themeName,
                themeData: this.themes[themeName]
            }
        });
        document.dispatchEvent(event);
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 监听系统设置页面的主题选择器
        const systemThemeSelector = document.getElementById('setting_theme_color');
        if (systemThemeSelector) {
            systemThemeSelector.addEventListener('change', (e) => {
                this.applyTheme(e.target.value);
            });
        }

        // 监听自定义主题切换器
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('theme-option')) {
                const theme = e.target.getAttribute('data-theme');
                if (theme) {
                    this.applyTheme(theme);
                }
            }
        });

        // 监听键盘快捷键（可选）
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.shiftKey && e.key === 'T') {
                this.showThemeSelector();
            }
        });
    }

    /**
     * 创建主题切换器
     */
    createThemeSwitcher() {
        // 检查是否已存在主题切换器
        if (document.getElementById('theme-switcher')) {
            return;
        }

        // 创建分类主题切换器HTML
        const switcherHtml = `
            <div id="theme-switcher" class="theme-switcher">
                <button class="btn btn-sm btn-outline-secondary" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <i class="fas fa-palette"></i> 主题
                </button>
                <div class="dropdown-menu dropdown-menu-right" style="min-width: 320px; max-height: 500px; overflow-y: auto;">
                    <div class="dropdown-header d-flex align-items-center justify-content-between">
                        <span><i class="fas fa-palette"></i> 选择主题颜色</span>
                        <small class="text-muted">${Object.keys(this.themes).length} 个主题</small>
                    </div>
                    ${this.renderThemesByCategory()}
                    <div class="dropdown-divider"></div>
                    <div class="dropdown-item-text">
                        <small class="text-muted">
                            <i class="fas fa-keyboard"></i> 快捷键: Ctrl+Shift+T
                        </small>
                    </div>
                </div>
            </div>
        `;

        // 将主题切换器添加到导航栏
        const navbar = document.querySelector('.navbar-nav');
        if (navbar) {
            const li = document.createElement('li');
            li.className = 'nav-item dropdown';
            li.innerHTML = switcherHtml;
            navbar.appendChild(li);
        }
    }

    /**
     * 按分类渲染主题
     */
    renderThemesByCategory() {
        let html = '';

        // 按分类组织主题
        const themesByCategory = {};
        Object.entries(this.themes).forEach(([key, theme]) => {
            const category = theme.category || 'other';
            if (!themesByCategory[category]) {
                themesByCategory[category] = [];
            }
            themesByCategory[category].push([key, theme]);
        });

        // 渲染每个分类
        Object.entries(themesByCategory).forEach(([category, themes]) => {
            const categoryInfo = this.themeCategories[category] || {
                name: '其他',
                icon: '🎨',
                description: '其他主题'
            };

            html += `
                <div class="theme-category-section">
                    <h6 class="dropdown-header">
                        <span class="category-icon">${categoryInfo.icon}</span>
                        ${categoryInfo.name}
                        <small class="text-muted">(${themes.length})</small>
                    </h6>
                    ${themes.map(([key, theme]) => `
                        <a class="dropdown-item theme-option d-flex align-items-center ${key === this.currentTheme ? 'active' : ''}"
                           href="#" data-theme="${key}" title="${theme.description}">
                            <div class="theme-preview-container me-3">
                                <div class="theme-preview-box" style="background: ${theme.color};">
                                    <div class="theme-accent-dot" style="background: ${theme.accent || theme.color};"></div>
                                </div>
                            </div>
                            <div class="theme-info flex-grow-1">
                                <div class="theme-name">${theme.name}</div>
                                <small class="theme-description text-muted">${theme.description}</small>
                            </div>
                            ${key === this.currentTheme ? '<i class="fas fa-check text-success ms-2"></i>' : ''}
                        </a>
                    `).join('')}
                </div>
            `;
        });

        return html;
    }

    /**
     * 更新主题选择器状态
     */
    updateThemeSelectorState(themeName) {
        // 更新系统设置页面的选择器
        const systemSelector = document.getElementById('setting_theme_color');
        if (systemSelector) {
            systemSelector.value = themeName;
        }

        // 更新自定义主题切换器的状态
        const themeOptions = document.querySelectorAll('.theme-option');
        themeOptions.forEach(option => {
            option.classList.remove('active');
            if (option.getAttribute('data-theme') === themeName) {
                option.classList.add('active');
            }
        });
    }

    /**
     * 显示主题选择器
     */
    showThemeSelector() {
        const switcher = document.getElementById('theme-switcher');
        if (switcher) {
            const dropdown = switcher.querySelector('.dropdown-menu');
            dropdown.classList.toggle('show');
        }
    }

    /**
     * 获取当前主题
     */
    getCurrentTheme() {
        return this.currentTheme;
    }

    /**
     * 获取所有可用主题
     */
    getAvailableThemes() {
        return this.themes;
    }

    /**
     * 预览主题（临时应用，不保存）
     */
    previewTheme(themeName) {
        if (!this.themes[themeName]) {
            return;
        }

        // 临时应用主题
        document.documentElement.setAttribute('data-theme', themeName);

        // 3秒后恢复原主题
        setTimeout(() => {
            document.documentElement.setAttribute('data-theme', this.currentTheme);
        }, 3000);
    }

    /**
     * 重置为默认主题
     */
    resetToDefault() {
        this.applyTheme('primary');
    }

    /**
     * 获取主题分类
     */
    getThemesByCategory() {
        const themesByCategory = {};
        Object.entries(this.themes).forEach(([key, theme]) => {
            const category = theme.category || 'other';
            if (!themesByCategory[category]) {
                themesByCategory[category] = [];
            }
            themesByCategory[category].push({ key, ...theme });
        });
        return themesByCategory;
    }

    /**
     * 获取推荐主题（基于时间和使用场景）
     */
    getRecommendedThemes() {
        const hour = new Date().getHours();
        const recommendations = [];

        // 根据时间推荐
        if (hour >= 18 || hour <= 6) {
            // 晚上推荐深色主题
            recommendations.push('dark', 'modern');
        } else {
            // 白天推荐明亮主题
            recommendations.push('primary', 'success', 'warning');
        }

        // 根据季节推荐（简单示例）
        const month = new Date().getMonth();
        if (month >= 2 && month <= 4) { // 春季
            recommendations.push('success', 'nature');
        } else if (month >= 5 && month <= 7) { // 夏季
            recommendations.push('warning', 'energy');
        } else if (month >= 8 && month <= 10) { // 秋季
            recommendations.push('classic-neutral', 'elegant');
        } else { // 冬季
            recommendations.push('dark', 'modern-neutral');
        }

        return [...new Set(recommendations)]; // 去重
    }

    /**
     * 随机切换主题
     */
    randomTheme() {
        const themeKeys = Object.keys(this.themes);
        const randomKey = themeKeys[Math.floor(Math.random() * themeKeys.length)];
        this.applyTheme(randomKey);

        // 显示随机主题通知
        if (window.toastr) {
            toastr.info(`随机切换到 ${this.themes[randomKey].name}`, '随机主题');
        }
    }

    /**
     * 主题收藏功能
     */
    toggleFavoriteTheme(themeName) {
        const favorites = this.getFavoriteThemes();
        const index = favorites.indexOf(themeName);

        if (index > -1) {
            favorites.splice(index, 1);
        } else {
            favorites.push(themeName);
        }

        localStorage.setItem('favorite-themes', JSON.stringify(favorites));
        this.updateThemeSelectorState(this.currentTheme); // 刷新界面
    }

    /**
     * 获取收藏的主题
     */
    getFavoriteThemes() {
        const saved = localStorage.getItem('favorite-themes');
        return saved ? JSON.parse(saved) : [];
    }

    /**
     * 主题使用统计
     */
    trackThemeUsage(themeName) {
        const usage = this.getThemeUsageStats();
        usage[themeName] = (usage[themeName] || 0) + 1;
        usage[themeName + '_lastUsed'] = Date.now();
        localStorage.setItem('theme-usage-stats', JSON.stringify(usage));
    }

    /**
     * 获取主题使用统计
     */
    getThemeUsageStats() {
        const saved = localStorage.getItem('theme-usage-stats');
        return saved ? JSON.parse(saved) : {};
    }

    /**
     * 获取最常用的主题
     */
    getMostUsedThemes(limit = 5) {
        const usage = this.getThemeUsageStats();
        const themeUsage = [];

        Object.entries(usage).forEach(([key, value]) => {
            if (!key.endsWith('_lastUsed') && typeof value === 'number') {
                themeUsage.push({ theme: key, count: value });
            }
        });

        return themeUsage
            .sort((a, b) => b.count - a.count)
            .slice(0, limit)
            .map(item => item.theme);
    }

    /**
     * 导出主题配置
     */
    exportThemeConfig() {
        const config = {
            currentTheme: this.currentTheme,
            favorites: this.getFavoriteThemes(),
            usage: this.getThemeUsageStats(),
            exportDate: new Date().toISOString()
        };

        const blob = new Blob([JSON.stringify(config, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'theme-config.json';
        a.click();
        URL.revokeObjectURL(url);
    }

    /**
     * 导入主题配置
     */
    importThemeConfig(file) {
        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                const config = JSON.parse(e.target.result);

                if (config.currentTheme && this.themes[config.currentTheme]) {
                    this.applyTheme(config.currentTheme);
                }

                if (config.favorites) {
                    localStorage.setItem('favorite-themes', JSON.stringify(config.favorites));
                }

                if (config.usage) {
                    localStorage.setItem('theme-usage-stats', JSON.stringify(config.usage));
                }

                if (window.toastr) {
                    toastr.success('主题配置导入成功', '导入完成');
                }
            } catch (error) {
                if (window.toastr) {
                    toastr.error('主题配置文件格式错误', '导入失败');
                }
            }
        };
        reader.readAsText(file);
    }

    /**
     * 保存主题设置到服务器
     */
    saveThemeToServer(themeName) {
        // 检查是否有CSRF token
        const csrfToken = document.querySelector('meta[name="csrf-token"]');
        if (!csrfToken) {
            console.warn('未找到CSRF token，无法保存主题到服务器');
            return;
        }

        // 发送AJAX请求保存主题设置
        fetch('/admin/system/settings/update', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-CSRFToken': csrfToken.getAttribute('content')
            },
            body: `setting_theme_color=${themeName}&value_type_theme_color=string`
        })
        .then(response => {
            if (response.ok) {
                console.log('主题设置已保存到服务器');
            } else {
                console.warn('保存主题设置到服务器失败');
            }
        })
        .catch(error => {
            console.warn('保存主题设置时发生错误:', error);
        });
    }
}

// 全局主题切换器实例
let themeSwitcher;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    themeSwitcher = new ThemeSwitcher();

    // 将实例暴露到全局作用域，方便其他脚本使用
    window.themeSwitcher = themeSwitcher;
});

// 监听主题变更事件的示例
document.addEventListener('themeChanged', function(e) {
    console.log('主题已变更:', e.detail);

    // 可以在这里添加主题变更后的额外处理逻辑
    // 例如：更新图表颜色、重新渲染某些组件等
});

// 导出类（如果使用模块系统）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ThemeSwitcher;
}
