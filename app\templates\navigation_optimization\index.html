{% extends "base.html" %}

{% block title %}导航菜单优化{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="row mb-4 page-header">
        <div class="col-md-8">
            <h2><i class="fas fa-bars"></i> 导航菜单优化</h2>
            <p class="text-muted">智能优化导航菜单，提升使用体验</p>
        </div>
        <div class="col-md-4">
            <div class="page-actions">
                <a href="{{ url_for('navigation_optimization.demo') }}" class="btn btn-info" target="_blank">
                    <i class="fas fa-external-link-alt"></i> 查看演示
                </a>
                <a href="{{ url_for('navigation_optimization.compare') }}" class="btn btn-outline-primary">
                    <i class="fas fa-balance-scale"></i> 对比分析
                </a>
            </div>
        </div>
    </div>

    <!-- 当前状态 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-info-circle"></i> 当前状态</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="status-item">
                                <strong>导航模式：</strong>
                                <span class="badge badge-primary" id="current-mode">
                                    {{ navigation_mode|default('auto') }}
                                </span>
                            </div>
                            <div class="status-item mt-2">
                                <strong>菜单类型：</strong>
                                {% if use_optimized_menu %}
                                    <span class="badge badge-success">优化菜单</span>
                                {% else %}
                                    <span class="badge badge-secondary">原始菜单</span>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            {% if menu_stats %}
                            <div class="status-item">
                                <strong>菜单统计：</strong>
                                <small class="text-muted d-block">
                                    原始: {{ menu_stats.original.main_menus }} 个主菜单 → 
                                    优化: {{ menu_stats.optimized.main_menus }} 个主菜单
                                    (减少 {{ menu_stats.reduction.percentage }}%)
                                </small>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 快速操作 -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h6><i class="fas fa-rocket"></i> 快速切换</h6>
                </div>
                <div class="card-body">
                    {% if not use_optimized_menu %}
                    <div class="alert alert-info">
                        <h6><i class="fas fa-lightbulb"></i> 推荐使用优化菜单</h6>
                        <p class="mb-2">优化后的菜单将主菜单从9+个减少到5个，提升使用体验。</p>
                        <a href="{{ url_for('navigation_optimization.switch_to_optimized') }}" class="btn btn-success btn-sm">
                            <i class="fas fa-toggle-on"></i> 启用优化菜单
                        </a>
                    </div>
                    {% else %}
                    <div class="alert alert-success">
                        <h6><i class="fas fa-check-circle"></i> 已启用优化菜单</h6>
                        <p class="mb-2">您正在使用优化后的5个主菜单，体验更佳！</p>
                        <a href="{{ url_for('navigation_optimization.switch_to_original') }}" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-toggle-off"></i> 切换回原始菜单
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h6><i class="fas fa-cogs"></i> 导航设置</h6>
                </div>
                <div class="card-body">
                    <div class="form-group">
                        <label>显示模式</label>
                        <select class="form-control" id="navigation-mode">
                            <option value="auto" {{ 'selected' if navigation_mode == 'auto' }}>自动适配</option>
                            <option value="desktop" {{ 'selected' if navigation_mode == 'desktop' }}>桌面模式</option>
                            <option value="compact" {{ 'selected' if navigation_mode == 'compact' }}>紧凑模式</option>
                            <option value="mobile" {{ 'selected' if navigation_mode == 'mobile' }}>移动模式</option>
                            <option value="minimal" {{ 'selected' if navigation_mode == 'minimal' }}>极简模式</option>
                        </select>
                    </div>
                    <button type="button" class="btn btn-primary btn-sm" onclick="saveNavigationMode()">
                        <i class="fas fa-save"></i> 保存设置
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 优化效果展示 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-chart-bar"></i> 优化效果</h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-3">
                            <div class="stat-card">
                                <div class="stat-number text-danger">9+</div>
                                <div class="stat-label">原主菜单数</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-card">
                                <div class="stat-number text-success">5</div>
                                <div class="stat-label">优化后主菜单</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-card">
                                <div class="stat-number text-primary">44%</div>
                                <div class="stat-label">菜单减少比例</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-card">
                                <div class="stat-number text-info">100%</div>
                                <div class="stat-label">功能保留率</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 菜单使用统计 -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-chart-pie"></i> 菜单使用统计</h5>
                </div>
                <div class="card-body">
                    <div id="usage-stats-container">
                        <p class="text-muted">正在加载使用统计...</p>
                    </div>
                    <div class="mt-3">
                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="resetUsageStats()">
                            <i class="fas fa-undo"></i> 重置统计
                        </button>
                        <button type="button" class="btn btn-outline-info btn-sm" onclick="exportConfig()">
                            <i class="fas fa-download"></i> 导出配置
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.status-item {
    padding: 8px 0;
}

.stat-card {
    padding: 20px;
    border-radius: 8px;
    background: var(--theme-surface, #f8f9fa);
    margin-bottom: 20px;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 8px;
}

.stat-label {
    font-size: 0.9rem;
    color: var(--theme-text-secondary, #6c757d);
}

.usage-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid var(--theme-border, #e5e7eb);
}

.usage-item:last-child {
    border-bottom: none;
}

.usage-count {
    font-weight: 500;
    color: var(--theme-primary, #007bff);
}
</style>

<script>
// 保存导航模式
function saveNavigationMode() {
    const mode = document.getElementById('navigation-mode').value;
    
    fetch('{{ url_for("navigation_optimization.toggle_mode") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ mode: mode })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            document.getElementById('current-mode').textContent = mode;
            toastr.success(data.message);
            
            // 刷新页面以应用新设置
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            toastr.error('设置保存失败');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        toastr.error('网络错误');
    });
}

// 重置使用统计
function resetUsageStats() {
    if (!confirm('确定要重置菜单使用统计吗？')) {
        return;
    }
    
    fetch('{{ url_for("navigation_optimization.reset_usage") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            toastr.success(data.message);
            loadUsageStats();
        } else {
            toastr.error('重置失败');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        toastr.error('网络错误');
    });
}

// 导出配置
function exportConfig() {
    fetch('{{ url_for("navigation_optimization.export_config") }}')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const blob = new Blob([JSON.stringify(data.config, null, 2)], {
                type: 'application/json'
            });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = data.filename;
            a.click();
            URL.revokeObjectURL(url);
            
            toastr.success('配置已导出');
        } else {
            toastr.error('导出失败');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        toastr.error('网络错误');
    });
}

// 加载使用统计
function loadUsageStats() {
    // 这里可以添加加载使用统计的逻辑
    const container = document.getElementById('usage-stats-container');
    container.innerHTML = '<p class="text-muted">暂无使用统计数据</p>';
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    loadUsageStats();
    
    // 显示欢迎提示
    setTimeout(() => {
        toastr.info('导航菜单优化功能已加载，您可以切换到优化菜单体验更佳的导航！');
    }, 1000);
});
</script>
{% endblock %}
