-- 创建引导场景相关表
-- 用于存储和管理不同类型学校的引导场景配置

-- 引导场景配置表
CREATE TABLE guide_scenarios (
    id INT IDENTITY(1,1) PRIMARY KEY,
    scenario_type NVARCHAR(50) UNIQUE NOT NULL,  -- 场景类型标识符
    name NVARCHAR(100) NOT NULL,                 -- 场景显示名称
    description NTEXT,                           -- 场景描述

    -- 场景特征和关注点
    characteristics NTEXT,                       -- 场景特征，JSON格式存储
    focus_areas NTEXT,                          -- 关注重点，JSON格式存储

    -- 引导配置
    welcome_message NTEXT,                      -- 欢迎消息
    priority_steps NTEXT,                       -- 优先步骤，JSON格式存储
    customized_content NTEXT,                  -- 定制化内容，JSON格式存储

    -- 配置选项
    simplified_mode BIT DEFAULT 0,              -- 是否启用简化模式
    advanced_features BIT DEFAULT 0,            -- 是否启用高级功能
    is_active BIT DEFAULT 1,                    -- 是否启用此场景

    -- 样式配置
    theme_color NVARCHAR(20) DEFAULT 'primary', -- 主题色彩
    icon NVARCHAR(50) DEFAULT 'fas fa-school',  -- 图标

    -- 统计信息
    usage_count INT DEFAULT 0,                  -- 使用次数
    last_used_at DATETIME2(1),                  -- 最后使用时间

    -- 时间戳
    created_at DATETIME2(1) DEFAULT GETDATE() NOT NULL,
    updated_at DATETIME2(1) DEFAULT GETDATE() NOT NULL,
    created_by INT,                             -- 创建者
    updated_by INT,                             -- 更新者

    FOREIGN KEY (created_by) REFERENCES users(id),
    FOREIGN KEY (updated_by) REFERENCES users(id)
);

-- 引导场景使用记录表
CREATE TABLE guide_scenario_usage (
    id INT IDENTITY(1,1) PRIMARY KEY,
    scenario_type NVARCHAR(50) NOT NULL,        -- 场景类型
    user_id INT NOT NULL,                       -- 用户ID
    area_id INT,                                -- 学校ID

    -- 使用情况
    started_at DATETIME2(1) DEFAULT GETDATE() NOT NULL, -- 开始时间
    completed_at DATETIME2(1),                  -- 完成时间
    is_completed BIT DEFAULT 0,                 -- 是否完成
    current_step NVARCHAR(50),                  -- 当前步骤
    completed_steps NTEXT,                      -- 已完成步骤，JSON格式

    -- 用户反馈
    satisfaction_rating INT,                    -- 满意度评分(1-5)
    feedback_text NTEXT,                        -- 反馈文本
    drop_off_step NVARCHAR(50),                 -- 跳出步骤
    drop_off_reason NVARCHAR(200),              -- 跳出原因

    -- 统计数据
    total_duration INT,                         -- 总用时(秒)
    step_durations NTEXT,                       -- 各步骤用时，JSON格式

    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (area_id) REFERENCES administrative_areas(id)
);

-- 引导场景反馈表
CREATE TABLE guide_scenario_feedback (
    id INT IDENTITY(1,1) PRIMARY KEY,
    scenario_type NVARCHAR(50) NOT NULL,        -- 场景类型
    user_id INT NOT NULL,                       -- 用户ID
    usage_id INT,                               -- 使用记录ID

    -- 反馈内容
    step_name NVARCHAR(50),                     -- 反馈步骤
    rating INT,                                 -- 评分(1-5)
    feedback_type NVARCHAR(20),                 -- 反馈类型：suggestion/bug/praise
    feedback_text NTEXT,                        -- 反馈内容

    -- 处理状态
    is_processed BIT DEFAULT 0,                 -- 是否已处理
    processed_by INT,                           -- 处理人
    processed_at DATETIME2(1),                  -- 处理时间
    response_text NTEXT,                        -- 回复内容

    created_at DATETIME2(1) DEFAULT GETDATE() NOT NULL,

    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (usage_id) REFERENCES guide_scenario_usage(id),
    FOREIGN KEY (processed_by) REFERENCES users(id)
);

-- 创建索引
CREATE INDEX IX_guide_scenarios_scenario_type ON guide_scenarios(scenario_type);
CREATE INDEX IX_guide_scenarios_is_active ON guide_scenarios(is_active);
CREATE INDEX IX_guide_scenario_usage_scenario_type ON guide_scenario_usage(scenario_type);
CREATE INDEX IX_guide_scenario_usage_user_id ON guide_scenario_usage(user_id);
CREATE INDEX IX_guide_scenario_usage_started_at ON guide_scenario_usage(started_at);
CREATE INDEX IX_guide_scenario_feedback_scenario_type ON guide_scenario_feedback(scenario_type);
CREATE INDEX IX_guide_scenario_feedback_is_processed ON guide_scenario_feedback(is_processed);

-- 插入默认场景配置
INSERT INTO guide_scenarios (scenario_type, name, description, characteristics, focus_areas, welcome_message, priority_steps, simplified_mode, theme_color, icon) VALUES
('primary', '小学', '适用于小学的食堂管理引导',
 '["学生年龄较小", "营养需求特殊", "安全要求严格", "家长关注度高"]',
 '["营养搭配", "食品安全", "过敏管理", "卫生标准"]',
 '欢迎使用专为小学设计的食堂管理系统！我们将重点关注营养搭配和食品安全。',
 '["daily_management", "food_samples", "suppliers", "weekly_menu"]',
 0, 'primary', 'fas fa-child'),

('middle', '中学', '适用于中学的食堂管理引导',
 '["学生食量增大", "口味偏好明显", "营养需求多样", "管理相对复杂"]',
 '["营养均衡", "口味调配", "成本控制", "效率提升"]',
 '欢迎使用中学食堂管理系统！我们将帮助您平衡营养、口味和成本。',
 '["weekly_menu", "suppliers", "daily_management", "purchase_order"]',
 0, 'success', 'fas fa-user-graduate'),

('high', '高中', '适用于高中的食堂管理引导',
 '["学生自主性强", "时间安排紧密", "营养需求高", "管理标准化"]',
 '["高效管理", "营养科学", "成本优化", "质量控制"]',
 '欢迎使用高中食堂管理系统！我们将重点关注高效管理和营养科学。',
 '["suppliers", "weekly_menu", "purchase_order", "stock_in"]',
 0, 'info', 'fas fa-graduation-cap'),

('vocational', '职业学校', '适用于职业学校的食堂管理引导',
 '["学生实践性强", "专业特色明显", "管理实用性", "技能培养"]',
 '["实践教学", "技能培训", "成本管理", "质量控制"]',
 '欢迎使用职业学校食堂管理系统！我们将结合实践教学和技能培养。',
 '["traceability", "stock_in", "suppliers", "daily_management"]',
 0, 'warning', 'fas fa-tools'),

('university', '大学', '适用于大学的食堂管理引导',
 '["规模较大", "管理复杂", "多样化需求", "标准化要求"]',
 '["规模化管理", "数据分析", "标准化流程", "质量提升"]',
 '欢迎使用大学食堂管理系统！我们将重点关注规模化管理和数据分析。',
 '["traceability", "purchase_order", "stock_in", "consumption_plan"]',
 1, 'danger', 'fas fa-university'),

('rural', '乡村学校', '适用于乡村学校的食堂管理引导',
 '["资源有限", "人员较少", "简化管理", "基础保障"]',
 '["简化流程", "基础功能", "人员培训", "成本控制"]',
 '欢迎使用适合乡村学校的简化食堂管理系统！我们将重点关注简化流程和基础功能。',
 '["suppliers", "weekly_menu", "daily_management", "stock_in"]',
 1, 'secondary', 'fas fa-home');

PRINT '引导场景相关表创建完成！';
PRINT '已插入 6 个默认场景配置：小学、中学、高中、职业学校、大学、乡村学校';

-- 分隔批次，以便创建触发器
GO

-- 添加触发器自动更新 updated_at 字段
CREATE TRIGGER tr_guide_scenarios_update
ON guide_scenarios
AFTER UPDATE
AS
BEGIN
    UPDATE guide_scenarios
    SET updated_at = GETDATE()
    WHERE id IN (SELECT id FROM inserted);
END;

GO

PRINT '触发器创建完成！';
