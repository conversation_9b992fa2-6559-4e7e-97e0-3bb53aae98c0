/**
 * 集成界面控制器 - 整合界面统一控制与视觉效果
 * 通过导航栏眼睛图标提供最小化、不遮挡界面的控制方案
 */

class IntegratedUIController {
    constructor() {
        this.settings = {
            // 界面统一控制
            fontSize: 'normal',           // xs, sm, normal, lg, xl
            tableStyle: 'unified',        // unified, compact, spacious, minimal
            tableDensity: 'normal',       // compact, normal, spacious
            spacing: 'normal',            // compact, normal, spacious
            
            // 视觉效果控制
            disableBackdropBlur: true,    // 禁用背景模糊
            disableTextShadow: true,      // 禁用文字阴影
            disableImageBlur: true,       // 禁用图片模糊
            disableFocusBlur: true,       // 禁用焦点模糊
            
            // 表格优化
            improveTableReadability: true, // 改善表格可读性
            enhanceTableHeaders: true,     // 增强表头对比度
            fixTextBackgroundContrast: true, // 修复文字背景对比度
            
            // 快速模式
            quickMode: 'balanced'         // performance, balanced, visual
        };
        
        this.init();
    }

    init() {
        this.loadSettings();
        this.createNavbarControl();
        this.applySettings();
        this.bindEvents();
        this.fixExistingIssues();
    }

    // 创建导航栏控制器
    createNavbarControl() {
        // 查找导航栏
        const navbar = document.querySelector('.navbar-nav');
        if (!navbar) return;

        // 创建眼睛图标下拉菜单
        const controlItem = document.createElement('li');
        controlItem.className = 'nav-item dropdown';
        controlItem.innerHTML = `
            <a class="nav-link dropdown-toggle" href="#" id="uiControlDropdown" role="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                <i class="fas fa-eye"></i> 界面
            </a>
            <div class="dropdown-menu dropdown-menu-right ui-control-menu" aria-labelledby="uiControlDropdown">
                <div class="dropdown-header">
                    <i class="fas fa-eye"></i> 界面控制中心
                    <small class="text-muted d-block">优化界面显示效果</small>
                </div>
                
                <!-- 快速模式选择 -->
                <div class="dropdown-section">
                    <h6 class="dropdown-subheader">快速模式</h6>
                    <div class="quick-mode-buttons">
                        <button class="btn btn-sm btn-outline-success quick-mode-btn" data-mode="performance">
                            <i class="fas fa-tachometer-alt"></i> 性能优先
                        </button>
                        <button class="btn btn-sm btn-outline-primary quick-mode-btn active" data-mode="balanced">
                            <i class="fas fa-balance-scale"></i> 平衡模式
                        </button>
                        <button class="btn btn-sm btn-outline-info quick-mode-btn" data-mode="visual">
                            <i class="fas fa-palette"></i> 视觉优先
                        </button>
                    </div>
                </div>
                
                <div class="dropdown-divider"></div>
                
                <!-- 字体与表格控制 -->
                <div class="dropdown-section">
                    <h6 class="dropdown-subheader">字体与表格</h6>
                    <div class="control-row">
                        <label>字体大小</label>
                        <select class="form-control form-control-sm" id="navbar-font-size">
                            <option value="xs">极小</option>
                            <option value="sm">小</option>
                            <option value="normal" selected>正常</option>
                            <option value="lg">大</option>
                            <option value="xl">极大</option>
                        </select>
                    </div>
                    <div class="control-row">
                        <label>表格样式</label>
                        <select class="form-control form-control-sm" id="navbar-table-style">
                            <option value="unified" selected>统一样式</option>
                            <option value="compact">紧凑样式</option>
                            <option value="spacious">宽松样式</option>
                            <option value="minimal">极简样式</option>
                        </select>
                    </div>
                </div>
                
                <div class="dropdown-divider"></div>
                
                <!-- 视觉效果控制 -->
                <div class="dropdown-section">
                    <h6 class="dropdown-subheader">视觉效果</h6>
                    <div class="control-switches">
                        <div class="form-check form-check-sm">
                            <input class="form-check-input" type="checkbox" id="navbar-disable-blur" checked>
                            <label class="form-check-label" for="navbar-disable-blur">
                                禁用模糊效果
                            </label>
                        </div>
                        <div class="form-check form-check-sm">
                            <input class="form-check-input" type="checkbox" id="navbar-enhance-headers" checked>
                            <label class="form-check-label" for="navbar-enhance-headers">
                                增强表头对比度
                            </label>
                        </div>
                        <div class="form-check form-check-sm">
                            <input class="form-check-input" type="checkbox" id="navbar-fix-contrast" checked>
                            <label class="form-check-label" for="navbar-fix-contrast">
                                修复文字对比度
                            </label>
                        </div>
                    </div>
                </div>
                
                <div class="dropdown-divider"></div>
                
                <!-- 快速操作 -->
                <div class="dropdown-section">
                    <div class="quick-actions">
                        <button class="btn btn-sm btn-success btn-block" id="navbar-apply-optimal">
                            <i class="fas fa-magic"></i> 应用最佳设置
                        </button>
                        <div class="btn-group btn-group-sm w-100 mt-2">
                            <button class="btn btn-outline-secondary" id="navbar-reset-settings">
                                <i class="fas fa-undo"></i> 重置
                            </button>
                            <button class="btn btn-outline-info" id="navbar-export-settings">
                                <i class="fas fa-download"></i> 导出
                            </button>
                            <button class="btn btn-outline-warning" id="navbar-import-settings">
                                <i class="fas fa-upload"></i> 导入
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="dropdown-divider"></div>
                
                <!-- 状态显示 -->
                <div class="dropdown-section">
                    <small class="text-muted">
                        <i class="fas fa-info-circle"></i> 
                        当前模式: <span id="current-mode-display">平衡模式</span>
                    </small>
                </div>
            </div>
        `;

        // 插入到导航栏最后
        navbar.appendChild(controlItem);

        // 添加样式
        this.addNavbarControlStyles();
    }

    // 添加导航栏控制样式
    addNavbarControlStyles() {
        const style = document.createElement('style');
        style.id = 'integrated-ui-control-styles';
        style.textContent = `
            /* 导航栏界面控制样式 */
            .ui-control-menu {
                min-width: 320px;
                max-width: 380px;
                padding: 0;
                border: none;
                border-radius: 12px;
                box-shadow: 0 10px 30px rgba(0,0,0,0.15);
                background: var(--theme-surface, #ffffff);
            }
            
            .ui-control-menu .dropdown-header {
                background: linear-gradient(135deg, var(--theme-primary) 0%, var(--theme-primary-light) 100%);
                color: white;
                padding: 12px 16px;
                border-radius: 12px 12px 0 0;
                border-bottom: none;
                font-weight: 600;
            }
            
            .dropdown-section {
                padding: 12px 16px;
            }
            
            .dropdown-subheader {
                font-size: 13px;
                font-weight: 600;
                color: var(--theme-primary, #007bff);
                margin-bottom: 8px;
                display: flex;
                align-items: center;
                gap: 6px;
            }
            
            .dropdown-subheader::before {
                content: '';
                width: 3px;
                height: 12px;
                background: var(--theme-primary, #007bff);
                border-radius: 2px;
            }
            
            .quick-mode-buttons {
                display: flex;
                gap: 4px;
                margin-bottom: 8px;
            }
            
            .quick-mode-btn {
                flex: 1;
                font-size: 11px;
                padding: 6px 4px;
                border-radius: 6px;
                transition: all 0.2s ease;
            }
            
            .quick-mode-btn.active {
                background-color: var(--theme-primary, #007bff);
                border-color: var(--theme-primary, #007bff);
                color: white;
                box-shadow: 0 2px 4px rgba(0,123,255,0.3);
            }
            
            .control-row {
                margin-bottom: 8px;
            }
            
            .control-row label {
                font-size: 12px;
                font-weight: 500;
                color: var(--theme-text, #333);
                margin-bottom: 4px;
                display: block;
            }
            
            .control-row .form-control {
                font-size: 12px;
                height: 28px;
                border-radius: 6px;
            }
            
            .control-switches {
                display: flex;
                flex-direction: column;
                gap: 6px;
            }
            
            .form-check-sm {
                margin-bottom: 0;
            }
            
            .form-check-sm .form-check-input {
                margin-top: 2px;
            }
            
            .form-check-sm .form-check-label {
                font-size: 12px;
                color: var(--theme-text, #333);
            }
            
            .quick-actions .btn {
                font-size: 12px;
                border-radius: 6px;
            }
            
            .ui-control-menu .dropdown-divider {
                margin: 0;
                border-color: var(--theme-border, #e5e7eb);
            }
            
            /* 响应式优化 */
            @media (max-width: 768px) {
                .ui-control-menu {
                    min-width: 280px;
                    max-width: 300px;
                }
                
                .quick-mode-btn {
                    font-size: 10px;
                    padding: 4px 2px;
                }
            }
            
            /* 深色主题适配 */
            [data-theme="dark"] .ui-control-menu {
                background: var(--theme-surface, #1e293b);
                border: 1px solid var(--theme-border, #334155);
            }
            
            [data-theme="dark"] .dropdown-divider {
                border-color: var(--theme-border, #334155);
            }
        `;
        document.head.appendChild(style);
    }

    // 绑定事件
    bindEvents() {
        // 快速模式切换
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('quick-mode-btn')) {
                this.handleQuickModeChange(e.target);
            }
        });

        // 最佳设置应用
        const applyOptimalBtn = document.getElementById('navbar-apply-optimal');
        if (applyOptimalBtn) {
            applyOptimalBtn.addEventListener('click', () => {
                this.applyOptimalSettings();
            });
        }

        // 重置设置
        const resetBtn = document.getElementById('navbar-reset-settings');
        if (resetBtn) {
            resetBtn.addEventListener('click', () => {
                this.resetToDefaults();
            });
        }

        // 实时控制
        const controls = [
            'navbar-font-size', 'navbar-table-style', 
            'navbar-disable-blur', 'navbar-enhance-headers', 'navbar-fix-contrast'
        ];
        
        controls.forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.addEventListener('change', () => {
                    this.updateSettingsFromNavbar();
                    this.applySettings();
                });
            }
        });

        // 全局快捷键
        document.addEventListener('keydown', (e) => {
            // Ctrl+Shift+E 快速切换界面控制
            if (e.ctrlKey && e.shiftKey && e.key === 'E') {
                e.preventDefault();
                this.toggleNavbarDropdown();
            }
        });
    }

    // 处理快速模式切换
    handleQuickModeChange(button) {
        const mode = button.dataset.mode;
        
        // 更新按钮状态
        document.querySelectorAll('.quick-mode-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        button.classList.add('active');
        
        // 应用对应模式
        this.applyQuickMode(mode);
        
        // 更新显示
        const modeNames = {
            'performance': '性能优先',
            'balanced': '平衡模式',
            'visual': '视觉优先'
        };
        
        const display = document.getElementById('current-mode-display');
        if (display) {
            display.textContent = modeNames[mode];
        }
        
        this.showToast(`已切换到${modeNames[mode]}`, 'success');
    }

    // 应用快速模式
    applyQuickMode(mode) {
        switch (mode) {
            case 'performance':
                this.settings = {
                    ...this.settings,
                    fontSize: 'sm',
                    tableStyle: 'minimal',
                    tableDensity: 'compact',
                    spacing: 'compact',
                    disableBackdropBlur: true,
                    disableTextShadow: true,
                    disableImageBlur: true,
                    disableFocusBlur: true,
                    improveTableReadability: true,
                    enhanceTableHeaders: true,
                    fixTextBackgroundContrast: true,
                    quickMode: 'performance'
                };
                break;
                
            case 'balanced':
                this.settings = {
                    ...this.settings,
                    fontSize: 'normal',
                    tableStyle: 'unified',
                    tableDensity: 'normal',
                    spacing: 'normal',
                    disableBackdropBlur: true,
                    disableTextShadow: true,
                    disableImageBlur: true,
                    disableFocusBlur: true,
                    improveTableReadability: true,
                    enhanceTableHeaders: true,
                    fixTextBackgroundContrast: true,
                    quickMode: 'balanced'
                };
                break;
                
            case 'visual':
                this.settings = {
                    ...this.settings,
                    fontSize: 'lg',
                    tableStyle: 'spacious',
                    tableDensity: 'spacious',
                    spacing: 'spacious',
                    disableBackdropBlur: false,
                    disableTextShadow: false,
                    disableImageBlur: false,
                    disableFocusBlur: true,
                    improveTableReadability: true,
                    enhanceTableHeaders: true,
                    fixTextBackgroundContrast: true,
                    quickMode: 'visual'
                };
                break;
        }
        
        this.applySettings();
        this.updateNavbarFromSettings();
        this.saveSettings();
    }

    // 应用最佳设置（基于采购订单页面）
    applyOptimalSettings() {
        this.settings = {
            fontSize: 'normal',
            tableStyle: 'unified',
            tableDensity: 'normal',
            spacing: 'normal',
            disableBackdropBlur: true,
            disableTextShadow: true,
            disableImageBlur: true,
            disableFocusBlur: true,
            improveTableReadability: true,
            enhanceTableHeaders: true,
            fixTextBackgroundContrast: true,
            quickMode: 'balanced'
        };
        
        this.applySettings();
        this.updateNavbarFromSettings();
        this.saveSettings();
        this.showToast('已应用最佳设置（基于采购订单页面）', 'success');
    }

    // 应用设置
    applySettings() {
        this.applyFontSettings();
        this.applyTableSettings();
        this.applyVisualEffects();
        this.fixTableReadability();
        this.enhanceTableHeaders();
        this.fixTextBackgroundContrast();
    }

    // 应用字体设置
    applyFontSettings() {
        const fontScales = {
            'xs': 0.8, 'sm': 0.9, 'normal': 1.0, 'lg': 1.1, 'xl': 1.2
        };
        
        const scale = fontScales[this.settings.fontSize] || 1.0;
        document.documentElement.style.setProperty('--unified-font-scale', scale);
        
        // 应用到表格
        document.querySelectorAll('.table').forEach(table => {
            table.style.fontSize = `${1.125 * scale}rem`;
        });
    }

    // 应用表格设置
    applyTableSettings() {
        const body = document.body;
        
        // 移除旧类
        body.classList.remove('table-style-unified', 'table-style-compact', 'table-style-spacious', 'table-style-minimal');
        body.classList.remove('table-density-compact', 'table-density-normal', 'table-density-spacious');
        
        // 应用新类
        body.classList.add(`table-style-${this.settings.tableStyle}`);
        body.classList.add(`table-density-${this.settings.tableDensity}`);
    }

    // 应用视觉效果
    applyVisualEffects() {
        const body = document.body;
        
        body.classList.toggle('disable-backdrop-blur', this.settings.disableBackdropBlur);
        body.classList.toggle('disable-text-shadow', this.settings.disableTextShadow);
        body.classList.toggle('disable-image-blur', this.settings.disableImageBlur);
        body.classList.toggle('disable-focus-blur', this.settings.disableFocusBlur);
    }

    // 修复表格可读性
    fixTableReadability() {
        if (!this.settings.improveTableReadability) return;
        
        document.querySelectorAll('.table').forEach(table => {
            table.style.fontSize = '1.125rem';
            table.style.lineHeight = '1.4';
            
            // 确保表格单元格有足够的内边距
            table.querySelectorAll('td, th').forEach(cell => {
                if (!cell.style.padding) {
                    cell.style.padding = '12px 10px';
                }
            });
        });
    }

    // 增强表头对比度
    enhanceTableHeaders() {
        if (!this.settings.enhanceTableHeaders) return;
        
        document.querySelectorAll('.table thead th').forEach(th => {
            th.style.background = 'linear-gradient(135deg, var(--theme-primary) 0%, var(--theme-primary-light) 100%)';
            th.style.color = 'white';
            th.style.fontWeight = '600';
            th.style.textShadow = '0 1px 2px rgba(0,0,0,0.3)';
            th.style.fontSize = '1.125rem';
        });
    }

    // 修复文字背景对比度
    fixTextBackgroundContrast() {
        if (!this.settings.fixTextBackgroundContrast) return;
        
        // 修复表格中文字与背景融合的问题
        document.querySelectorAll('.table tbody td').forEach(td => {
            const computedStyle = window.getComputedStyle(td);
            const bgColor = computedStyle.backgroundColor;
            const textColor = computedStyle.color;
            
            // 如果背景色和文字色对比度不够，强制设置
            if (bgColor && bgColor !== 'rgba(0, 0, 0, 0)' && bgColor !== 'transparent') {
                td.style.color = '#333333';
                td.style.backgroundColor = 'rgba(255, 255, 255, 0.9)';
            }
        });
        
        // 修复其他可能的对比度问题
        document.querySelectorAll('.text-muted, .text-secondary').forEach(el => {
            el.style.color = '#6c757d';
        });
    }

    // 修复现有问题
    fixExistingIssues() {
        // 自动修复常见的界面问题
        setTimeout(() => {
            this.fixTableReadability();
            this.enhanceTableHeaders();
            this.fixTextBackgroundContrast();
        }, 500);
    }

    // 从导航栏更新设置
    updateSettingsFromNavbar() {
        this.settings.fontSize = document.getElementById('navbar-font-size')?.value || 'normal';
        this.settings.tableStyle = document.getElementById('navbar-table-style')?.value || 'unified';
        this.settings.disableBackdropBlur = document.getElementById('navbar-disable-blur')?.checked || false;
        this.settings.enhanceTableHeaders = document.getElementById('navbar-enhance-headers')?.checked || false;
        this.settings.fixTextBackgroundContrast = document.getElementById('navbar-fix-contrast')?.checked || false;
    }

    // 从设置更新导航栏
    updateNavbarFromSettings() {
        const elements = {
            'navbar-font-size': this.settings.fontSize,
            'navbar-table-style': this.settings.tableStyle
        };

        Object.entries(elements).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) element.value = value;
        });

        const checkboxes = {
            'navbar-disable-blur': this.settings.disableBackdropBlur,
            'navbar-enhance-headers': this.settings.enhanceTableHeaders,
            'navbar-fix-contrast': this.settings.fixTextBackgroundContrast
        };

        Object.entries(checkboxes).forEach(([id, checked]) => {
            const element = document.getElementById(id);
            if (element) element.checked = checked;
        });
    }

    // 切换导航栏下拉菜单
    toggleNavbarDropdown() {
        const dropdown = document.getElementById('uiControlDropdown');
        if (dropdown) {
            dropdown.click();
        }
    }

    // 重置为默认设置
    resetToDefaults() {
        this.settings = {
            fontSize: 'normal',
            tableStyle: 'unified',
            tableDensity: 'normal',
            spacing: 'normal',
            disableBackdropBlur: true,
            disableTextShadow: true,
            disableImageBlur: true,
            disableFocusBlur: true,
            improveTableReadability: true,
            enhanceTableHeaders: true,
            fixTextBackgroundContrast: true,
            quickMode: 'balanced'
        };
        
        this.applySettings();
        this.updateNavbarFromSettings();
        this.saveSettings();
        this.showToast('已重置为默认设置', 'info');
    }

    // 加载设置
    loadSettings() {
        const saved = localStorage.getItem('integratedUISettings');
        if (saved) {
            this.settings = { ...this.settings, ...JSON.parse(saved) };
        }
    }

    // 保存设置
    saveSettings() {
        localStorage.setItem('integratedUISettings', JSON.stringify(this.settings));
    }

    // 显示提示
    showToast(message, type = 'info') {
        // 创建简单的提示
        const toast = document.createElement('div');
        toast.className = `alert alert-${type} alert-dismissible fade show`;
        toast.style.cssText = `
            position: fixed;
            top: 70px;
            right: 20px;
            z-index: 10000;
            min-width: 250px;
            font-size: 14px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        `;
        toast.innerHTML = `
            <i class="fas fa-eye"></i> ${message}
            <button type="button" class="close" data-dismiss="alert">
                <span>&times;</span>
            </button>
        `;
        
        document.body.appendChild(toast);
        
        // 3秒后自动移除
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 3000);
    }
}

// 初始化集成界面控制器
document.addEventListener('DOMContentLoaded', function() {
    // 等待页面完全加载后初始化
    setTimeout(() => {
        window.integratedUIController = new IntegratedUIController();
        console.log('🎨 集成界面控制器已初始化');
    }, 500);
});

// 导出给其他脚本使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = IntegratedUIController;
}
