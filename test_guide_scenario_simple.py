#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
引导场景管理功能简化测试脚本
专门测试数据库和核心功能
"""

import sys
import os

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_database_only():
    """仅测试数据库连接和表"""
    try:
        from sqlalchemy import create_engine, text
        from config import Config
        
        # 直接连接数据库
        engine = create_engine(Config.SQLALCHEMY_DATABASE_URI)
        
        with engine.connect() as conn:
            # 测试场景表是否存在
            result = conn.execute(text("SELECT COUNT(*) as count FROM guide_scenarios")).fetchone()
            scenario_count = result[0] if result else 0
            
            print(f"✅ 数据库连接成功")
            print(f"✅ guide_scenarios 表存在，包含 {scenario_count} 个场景配置")
            
            # 查询场景数据
            scenarios = conn.execute(text("""
                SELECT scenario_type, name, theme_color, is_active 
                FROM guide_scenarios 
                WHERE is_active = 1
                ORDER BY scenario_type
            """)).fetchall()
            
            print(f"\n📋 活跃场景列表:")
            for scenario in scenarios:
                print(f"  - {scenario[0]}: {scenario[1]} ({scenario[2]})")
            
            # 测试其他表
            tables_to_check = [
                'guide_scenario_usage',
                'guide_scenario_feedback'
            ]
            
            print(f"\n🔍 检查其他表:")
            for table_name in tables_to_check:
                try:
                    result = conn.execute(text(f"SELECT COUNT(*) FROM {table_name}")).fetchone()
                    count = result[0] if result else 0
                    print(f"  ✅ {table_name}: {count} 条记录")
                except Exception as e:
                    print(f"  ❌ {table_name}: {str(e)}")
            
            return True
            
    except Exception as e:
        print(f"❌ 数据库测试失败: {str(e)}")
        return False

def test_scenario_models():
    """测试场景模型"""
    try:
        # 设置最小的Flask应用上下文
        from flask import Flask
        from flask_sqlalchemy import SQLAlchemy
        from config import Config
        
        app = Flask(__name__)
        app.config.from_object(Config)
        db = SQLAlchemy(app)
        
        with app.app_context():
            # 导入模型
            from app.models.guide_scenario import GuideScenario, GuideScenarioUsage, GuideScenarioFeedback
            
            print(f"🔧 测试场景模型:")
            
            # 测试查询场景
            scenarios = GuideScenario.query.filter_by(is_active=True).all()
            print(f"  ✅ 查询到 {len(scenarios)} 个活跃场景")
            
            for scenario in scenarios[:3]:  # 只显示前3个
                print(f"    - {scenario.scenario_type}: {scenario.name}")
                
                # 测试转换为字典
                scenario_dict = scenario.to_dict()
                print(f"      特征数量: {len(scenario_dict['characteristics'])}")
                print(f"      关注点数量: {len(scenario_dict['focus_areas'])}")
            
            # 测试使用记录模型
            usage_count = GuideScenarioUsage.query.count()
            print(f"  ✅ 使用记录表: {usage_count} 条记录")
            
            # 测试反馈模型
            feedback_count = GuideScenarioFeedback.query.count()
            print(f"  ✅ 反馈表: {feedback_count} 条记录")
            
            return True
            
    except Exception as e:
        print(f"❌ 模型测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_scenario_service_basic():
    """测试场景服务基础功能"""
    try:
        # 设置最小的Flask应用上下文
        from flask import Flask
        from flask_sqlalchemy import SQLAlchemy
        from config import Config
        
        app = Flask(__name__)
        app.config.from_object(Config)
        db = SQLAlchemy(app)
        
        with app.app_context():
            from app.services.scenario_guide_service import ScenarioGuideService
            
            print(f"🎯 测试场景服务基础功能:")
            
            # 测试从数据库加载场景
            print("  - 从数据库加载场景配置...")
            success = ScenarioGuideService.load_scenarios_from_database()
            print(f"    {'✅ 成功' if success else '❌ 失败'}")
            
            # 测试场景检测
            print("  - 测试智能场景检测...")
            test_schools = [
                "北京市朝阳区实验小学",
                "清华大学",
                "山村希望小学",
                "北京职业技术学院"
            ]
            
            for school_name in test_schools:
                detected = ScenarioGuideService.detect_school_type(school_name)
                print(f"    {school_name} -> {detected}")
            
            # 测试获取场景配置
            print("  - 测试获取场景配置...")
            scenario_types = ['primary', 'university', 'rural']
            for scenario_type in scenario_types:
                scenario_info = ScenarioGuideService.SCHOOL_TYPES.get(scenario_type)
                if scenario_info:
                    print(f"    {scenario_type}: {scenario_info['name']}")
                else:
                    print(f"    {scenario_type}: 未找到配置")
            
            return True
            
    except Exception as e:
        print(f"❌ 场景服务测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_create_scenario():
    """测试创建场景功能"""
    try:
        from flask import Flask
        from flask_sqlalchemy import SQLAlchemy
        from config import Config
        
        app = Flask(__name__)
        app.config.from_object(Config)
        db = SQLAlchemy(app)
        
        with app.app_context():
            from app.services.scenario_guide_service import ScenarioGuideService
            from app.models.guide_scenario import GuideScenario
            
            print(f"🆕 测试场景创建功能:")
            
            # 创建测试场景
            test_scenario = {
                'scenario_type': 'test_scenario_simple',
                'name': '简化测试场景',
                'description': '这是一个简化的测试场景',
                'characteristics': ['测试特征1', '测试特征2'],
                'focus_areas': ['测试重点1', '测试重点2'],
                'welcome_message': '欢迎使用简化测试场景！',
                'priority_steps': ['daily_management', 'suppliers'],
                'simplified_mode': True,
                'advanced_features': False
            }
            
            print("  - 创建测试场景...")
            success = ScenarioGuideService.create_new_scenario(test_scenario)
            print(f"    {'✅ 创建成功' if success else '❌ 创建失败'}")
            
            if success:
                # 验证场景是否已保存
                print("  - 验证场景是否已保存...")
                scenario = GuideScenario.query.filter_by(scenario_type='test_scenario_simple').first()
                
                if scenario:
                    print(f"    ✅ 场景已保存: {scenario.name}")
                    print(f"    场景ID: {scenario.id}")
                    print(f"    创建时间: {scenario.created_at}")
                    
                    # 清理测试数据
                    print("  - 清理测试数据...")
                    db.session.delete(scenario)
                    db.session.commit()
                    print("    ✅ 测试数据已清理")
                else:
                    print("    ❌ 场景未找到")
                    success = False
            
            return success
            
    except Exception as e:
        print(f"❌ 场景创建测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("引导场景管理功能简化测试")
    print("=" * 60)
    
    tests = [
        ("数据库连接测试", test_database_only),
        ("场景模型测试", test_scenario_models),
        ("场景服务基础测试", test_scenario_service_basic),
        ("场景创建测试", test_create_scenario)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🧪 {test_name}")
        print("-" * 40)
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}执行失败: {str(e)}")
            results.append((test_name, False))
    
    # 输出测试总结
    print("\n" + "=" * 60)
    print("测试结果总结")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("\n🎉 所有测试通过！引导场景管理功能数据库层面已就绪。")
        print("\n📝 下一步操作建议:")
        print("1. 启动应用服务器: python run.py")
        print("2. 访问 /admin/guide/scenarios 查看场景管理界面")
        print("3. 访问 /admin/guide/analytics 查看场景分析页面")
        print("4. 运行完整演示: python demo_guide_scenario.py")
    else:
        print(f"\n⚠️  有 {total - passed} 个测试失败，请检查相关配置。")
        
        if passed > 0:
            print("\n✅ 部分功能正常，可以尝试启动应用进行进一步测试。")
    
    return passed == total

if __name__ == '__main__':
    main()
