#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
引导场景管理功能演示脚本
展示如何使用引导场景管理系统
"""

import sys
import os

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from app.services.scenario_guide_service import ScenarioGuideService

def demo_scenario_management():
    """演示场景管理功能"""
    print("=" * 60)
    print("引导场景管理功能演示")
    print("=" * 60)
    
    # 1. 显示现有场景
    print("\n1. 现有场景配置:")
    print("-" * 40)
    for scenario_type, scenario_info in ScenarioGuideService.SCHOOL_TYPES.items():
        print(f"场景类型: {scenario_type}")
        print(f"  名称: {scenario_info['name']}")
        print(f"  特征: {', '.join(scenario_info['characteristics'])}")
        print(f"  关注点: {', '.join(scenario_info['focus_areas'])}")
        
        # 获取引导配置
        guide_config = ScenarioGuideService.SCENARIO_GUIDES.get(scenario_type, {})
        if guide_config:
            print(f"  欢迎消息: {guide_config.get('welcome_message', '无')[:50]}...")
            print(f"  优先步骤: {', '.join(guide_config.get('priority_steps', []))}")
            print(f"  简化模式: {'是' if guide_config.get('simplified_mode') else '否'}")
            print(f"  高级功能: {'是' if guide_config.get('advanced_features') else '否'}")
        print()
    
    # 2. 演示创建新场景
    print("\n2. 创建新场景演示:")
    print("-" * 40)
    new_scenario_data = {
        'scenario_type': 'kindergarten',
        'name': '幼儿园',
        'characteristics': ['年龄很小', '营养要求极高', '安全要求最严', '家长参与度高'],
        'focus_areas': ['营养科学', '食品安全', '过敏预防', '卫生管理'],
        'welcome_message': '欢迎使用专为幼儿园设计的食堂管理系统！我们将特别关注幼儿营养和安全。',
        'priority_steps': ['food_samples', 'daily_management', 'suppliers'],
        'simplified_mode': True,
        'advanced_features': False
    }
    
    print("正在创建新场景: 幼儿园")
    success = ScenarioGuideService.create_new_scenario(new_scenario_data)
    print(f"创建结果: {'成功' if success else '失败'}")
    
    # 3. 演示场景测试
    print("\n3. 场景测试演示:")
    print("-" * 40)
    test_scenarios = ['primary', 'kindergarten']
    for scenario_type in test_scenarios:
        print(f"测试场景: {scenario_type}")
        test_result = ScenarioGuideService.test_scenario_config(scenario_type, user_id=1)
        
        if test_result.get('success', True):  # 如果没有success字段，说明测试通过
            print(f"  场景名称: {test_result.get('scenario_name', '未知')}")
            print(f"  预计时间: {test_result.get('estimated_time', '未知')}")
            print(f"  总步骤数: {test_result.get('total_steps', 0)}")
            print(f"  优先步骤数: {test_result.get('priority_steps_count', 0)}")
            print(f"  定制内容数: {test_result.get('customized_content_count', 0)}")
            print(f"  验证通过: {'是' if test_result.get('validation_passed', True) else '否'}")
            
            issues = test_result.get('issues', [])
            if issues:
                print(f"  发现问题: {', '.join(issues)}")
        else:
            print(f"  测试失败: {test_result.get('message', '未知错误')}")
        print()
    
    # 4. 演示个性化引导计划
    print("\n4. 个性化引导计划演示:")
    print("-" * 40)
    for scenario_type in ['primary', 'university']:
        print(f"场景: {scenario_type}")
        guide_plan = ScenarioGuideService.create_personalized_guide_plan(scenario_type)
        
        print(f"  学校类型: {guide_plan['school_type']}")
        print(f"  欢迎消息: {guide_plan['welcome_message'][:50]}...")
        print(f"  预计时间: {guide_plan['estimated_time']}")
        print(f"  步骤总数: {len(guide_plan['steps'])}")
        
        print("  引导步骤:")
        for i, step in enumerate(guide_plan['steps'][:5]):  # 只显示前5个步骤
            priority_mark = " ⭐" if step['is_priority'] else ""
            print(f"    {i+1}. {step['step_name']}{priority_mark}")
        
        if len(guide_plan['steps']) > 5:
            print(f"    ... 还有 {len(guide_plan['steps']) - 5} 个步骤")
        print()
    
    # 5. 演示场景检测
    print("\n5. 场景自动检测演示:")
    print("-" * 40)
    test_school_names = [
        "北京市朝阳区实验小学",
        "清华大学",
        "山村希望小学",
        "北京职业技术学院",
        "育才中学"
    ]
    
    for school_name in test_school_names:
        detected_type = ScenarioGuideService.detect_school_type(school_name)
        print(f"学校: {school_name}")
        print(f"  检测结果: {detected_type}")
        
        if detected_type != 'unknown':
            scenario_info = ScenarioGuideService.SCHOOL_TYPES.get(detected_type, {})
            print(f"  场景名称: {scenario_info.get('name', '未知')}")
        print()
    
    # 6. 演示场景报告
    print("\n6. 场景分析报告演示:")
    print("-" * 40)
    for scenario_type in ['primary', 'middle', 'university']:
        report = ScenarioGuideService.generate_scenario_report(scenario_type)
        
        print(f"场景: {scenario_type}")
        print(f"  适用性评分: {report['suitability_score']}/10")
        print(f"  推荐理由: {', '.join(report['recommendations'][:2])}")
        print(f"  注意事项: {', '.join(report['considerations'][:2])}")
        print()

def demo_scenario_customization():
    """演示场景定制功能"""
    print("\n" + "=" * 60)
    print("场景定制功能演示")
    print("=" * 60)
    
    # 演示获取定制化内容
    print("\n1. 定制化内容演示:")
    print("-" * 40)
    
    scenarios_to_test = ['primary', 'university', 'rural']
    steps_to_test = ['daily_management', 'weekly_menu', 'suppliers']
    
    for scenario_type in scenarios_to_test:
        print(f"场景: {scenario_type}")
        scenario_name = ScenarioGuideService.SCHOOL_TYPES.get(scenario_type, {}).get('name', scenario_type)
        print(f"  名称: {scenario_name}")
        
        for step_name in steps_to_test:
            customized_content = ScenarioGuideService.get_customized_step_content(scenario_type, step_name)
            
            if customized_content:
                print(f"  步骤 {step_name}:")
                if 'emphasis' in customized_content:
                    print(f"    重点: {customized_content['emphasis'][:60]}...")
                if 'tips' in customized_content and customized_content['tips']:
                    print(f"    提示: {customized_content['tips'][0][:40]}...")
        print()

def main():
    """主函数"""
    # 创建应用上下文
    app = create_app()
    
    with app.app_context():
        try:
            # 运行演示
            demo_scenario_management()
            demo_scenario_customization()
            
            print("\n" + "=" * 60)
            print("演示完成！")
            print("=" * 60)
            print("\n功能特点:")
            print("✓ 多场景适配 - 支持小学、中学、高中、大学等不同类型学校")
            print("✓ 智能检测 - 根据学校名称自动识别学校类型")
            print("✓ 个性化引导 - 为不同场景提供定制化的引导流程")
            print("✓ 配置管理 - 支持创建、编辑、测试场景配置")
            print("✓ 效果分析 - 提供详细的使用统计和效果分析")
            print("✓ 持久化存储 - 支持数据库存储和管理")
            
            print("\n下一步开发建议:")
            print("1. 完善数据库模型和迁移脚本")
            print("2. 实现场景配置的可视化编辑器")
            print("3. 添加更多的场景分析和优化功能")
            print("4. 集成用户反馈和满意度调查")
            print("5. 实现场景配置的导入导出功能")
            
        except Exception as e:
            print(f"演示过程中出现错误: {str(e)}")
            import traceback
            traceback.print_exc()

if __name__ == '__main__':
    main()
