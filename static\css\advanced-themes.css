/* 专业主题颜色系统 - 基于色彩心理学和设计原理 */
:root {
  /* 默认主题颜色 (Ocean Blue - 海洋蓝) */
  --theme-primary: #2563eb; /* 主色 - 海洋蓝 */
  --theme-primary-50: #eff6ff;
  --theme-primary-100: #dbeafe;
  --theme-primary-200: #bfdbfe;
  --theme-primary-300: #93c5fd;
  --theme-primary-400: #60a5fa;
  --theme-primary-500: #3b82f6;
  --theme-primary-600: #2563eb;
  --theme-primary-700: #1d4ed8;
  --theme-primary-800: #1e40af;
  --theme-primary-900: #1e3a8a;
  --theme-primary-rgb: 37, 99, 235;

  /* 语义化颜色 */
  --theme-success: #10b981; /* 成功 - 鲜绿 */
  --theme-success-light: #34d399;
  --theme-success-dark: #059669;
  --theme-info: #2563eb; /* 信息 - 深蓝 */
  --theme-info-light: #60a5fa;
  --theme-info-dark: #1d4ed8;
  --theme-warning: #f59e0b; /* 警告 - 橙黄 */
  --theme-warning-light: #fecaca;
  --theme-warning-dark: #b45309;
  --theme-danger: #ef4444; /* 危险 - 红色 */
  --theme-danger-light: #fecaca;
  --theme-danger-dark: #b91c1c;

  /* 中性色调 */
  --theme-gray-50: #f9fafb;
  --theme-gray-100: #f3f4f6;
  --theme-gray-200: #e5e7eb;
  --theme-gray-300: #d1d5db;
  --theme-gray-400: #9ca3af;
  --theme-gray-500: #6b7280;
  --theme-gray-600: #4b5563;
  --theme-gray-700: #374151;
  --theme-gray-800: #1f2937;
  --theme-gray-900: #111827;

  /* 特殊效果 */
  --theme-shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --theme-shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --theme-shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
  --theme-shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1);

  /* 文本颜色 */
  --theme-text: #111827;
  --theme-text-secondary: #6b7280;
  --theme-text-muted: #9ca3af;

  /* 背景颜色 */
  --theme-background: #ffffff;
  --theme-surface: #f9fafb;
  --theme-surface-dark: #f3f4f6;

  /* 边框和分割线 */
  --theme-border: #e5e7eb;
  --theme-border-light: #f3f4f6;
  --theme-divider: #e5e7eb;
}

/* 主题切换系统 */
[data-theme="primary"] {
  /* 海洋蓝主题 - 专业、信任、稳定 */
  --theme-primary: #001F3F; /* 深海军蓝 */
  --theme-primary-light: #7FDBFF; /* 浅水蓝 */
  --theme-primary-dark: #001122; /* 深海蓝 */
  --theme-accent: #FF6B6B; /* 珊瑚橙 - 对比色 */
  --theme-surface: #F8F9FA; /* 珍珠白 */
  --theme-surface-dark: #CCD6DD; /* 银灰 */
  --theme-coral: #FF6B6B;
  --theme-pearl: #F8F9FA;
  --theme-silver: #CCD6DD;
}

[data-theme="modern"] {
  /* 现代科技主题 - 极简、未来感 */
  --theme-primary: #1E1E2E; /* 深空黑 */
  --theme-primary-light: #4B4B6B;
  --theme-primary-dark: #0F0F1A;
  --theme-accent: #00F5D4; /* 荧光绿 */
  --theme-surface: #1A1A2E;
  --theme-surface-dark: #16213E;
  --theme-neon-blue: #00F5D4;
  --theme-neon-pink: #FF00FF;
  --theme-neon-green: #00FF00;
  --theme-background: #0F0F1A;
  --theme-text: #FFFFFF;
  --theme-text-secondary: #B0B0B0;
}

[data-theme="nature"] {
  /* 自然绿主题 - 健康、成长 */
  --theme-primary: #4A7856; /* 苔藓绿 */
  --theme-primary-light: #87A96B;
  --theme-primary-dark: #2D4A35;
  --theme-accent: #BC8A5F; /* 黏土棕 */
  --theme-surface: #F0FDF4;
  --theme-surface-dark: #DCFCED;
  --theme-background: #F0FDF4;
}

[data-theme="energy"] {
  /* 活力橙主题 - 创新、温暖 */
  --theme-primary: #FF6B35; /* 日出色 */
  --theme-primary-light: #FF9F1C;
  --theme-primary-dark: #E55A2B;
  --theme-accent: #0353A4; /* 深海蓝 */
  --theme-surface: #FEF9C7;
  --theme-surface-dark: #FDF4DC;
  --theme-background: #FEF9C7;
}

[data-theme="elegant"] {
  /* 优雅紫主题 - 创新、神秘 */
  --theme-primary: #7B5CBF; /* 薰衣草紫 */
  --theme-primary-light: #A88BEF;
  --theme-primary-dark: #4C2BB9;
  --theme-accent: #22C55E; /* 翠绿 */
  --theme-surface: #FAF5FF;
  --theme-surface-dark: #F3E8FF;
  --theme-background: #FAF5FF;
}

[data-theme="danger"] {
  /* 深邃红主题 - 警示 */
  --theme-primary: #C91F37; /* 中国红 */
  --theme-primary-light: #DC3545;
  --theme-primary-dark: #900020;
  --theme-accent: #F87171; /* 珊瑚粉 */
  --theme-surface: #FEF2F2;
  --theme-surface-dark: #FEE2E2;
  --theme-background: #FEF2F2;
}

[data-theme="dark"] {
  /* 深色模式 - 护眼、专业 */
  --theme-primary: #4F46E5; /* 亮蓝 */
  --theme-primary-light: #6366F1;
  --theme-primary-dark: #4338CA;
  --theme-accent: #10B981; /* 绿松石 */
  --theme-surface: #1E293B;
  --theme-surface-dark: #0F172A;
  --theme-background: #0F172A;
  --theme-text: #ECEDEE;
  --theme-text-secondary: #94A3B8;
  --theme-text-muted: #64748B;
  --theme-border: #334155;
  --theme-border-light: #475569;
  --theme-divider: #334155;
}

/* 经典中性风 */
[data-theme="classic-neutral"] {
  --theme-primary: #8B4513; /* 巧克力棕 */
  --theme-primary-light: #A0522D;
  --theme-primary-dark: #654321;
  --theme-accent: #DAA520; /* 金黄 */
  --theme-surface: #FAF0E6;
  --theme-surface-dark: #F5DEB3;
  --theme-background: #FAF0E6;
}

/* 现代中性风 */
[data-theme="modern-neutral"] {
  --theme-primary: #475569; /* 石板灰 */
  --theme-primary-light: #64748B;
  --theme-primary-dark: #2D3748;
  --theme-accent: #F7FAFC; /* 浅灰白 */
  --theme-surface: #F7FAFC;
  --theme-surface-dark: #E2E8F0;
  --theme-background: #F7FAFC;
}

/* 特殊效果增强 */
[data-theme] {
  /* 所有主题通用增强 */
  --theme-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --theme-border-radius: 0.5rem;
  --theme-box-shadow: var(--theme-shadow-md);
}

/* 深色模式特殊处理 */
[data-theme="dark"] {
  --theme-box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
}

[data-theme="modern"] {
  --theme-box-shadow: 0 4px 6px rgba(0, 245, 212, 0.1);
}

/* 无障碍设计 */
@media (prefers-contrast: more) {
  :root {
    --theme-primary: #000066; /* 高对比度主色 */
    --theme-primary-rgb: 0, 0, 102;
    --theme-accent: #FF0000; /* 高对比度强调色 */
    --theme-accent-rgb: 255, 0, 0;
  }
}

/* 响应式调整 */
@media (max-width: 768px) {
  :root {
    --theme-border-radius: 0.375rem;
    --theme-box-shadow: var(--theme-shadow-sm);
  }
}

/* 动画效果 */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideInUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -30px, 0);
  }
  70% {
    transform: translate3d(0, -15px, 0);
  }
  90% {
    transform: translate3d(0, -4px, 0);
  }
}

/* 全局样式应用主题变量 */
body {
  background-color: var(--theme-background);
  color: var(--theme-text);
  transition: var(--theme-transition);
}

/* 组件样式示例 */
.btn {
  background-color: var(--theme-primary);
  color: white;
  border-radius: var(--theme-border-radius);
  padding: 0.5rem 1rem;
  transition: var(--theme-transition);
  box-shadow: var(--theme-box-shadow);
  border: none;
  cursor: pointer;
  font-weight: 500;
}

.btn:hover {
  background-color: var(--theme-primary-light);
  transform: translateY(-2px);
  box-shadow: var(--theme-shadow-lg);
}

.btn:active {
  transform: translateY(0);
  box-shadow: var(--theme-shadow-sm);
}

.btn-secondary {
  background-color: var(--theme-gray-500);
  color: white;
}

.btn-secondary:hover {
  background-color: var(--theme-gray-600);
}

.btn-success {
  background-color: var(--theme-success);
}

.btn-success:hover {
  background-color: var(--theme-success-dark);
}

.btn-warning {
  background-color: var(--theme-warning);
}

.btn-warning:hover {
  background-color: var(--theme-warning-dark);
}

.btn-danger {
  background-color: var(--theme-danger);
}

.btn-danger:hover {
  background-color: var(--theme-danger-dark);
}

/* 表单元素 */
.form-control {
  background-color: var(--theme-surface);
  color: var(--theme-text);
  border: 1px solid var(--theme-border);
  border-radius: var(--theme-border-radius);
  padding: 0.5rem;
  transition: var(--theme-transition);
}

.form-control:focus {
  outline: none;
  border-color: var(--theme-primary);
  box-shadow: 0 0 0 2px rgba(var(--theme-primary-rgb), 0.2);
}

/* 卡片样式 */
.card {
  background-color: var(--theme-surface);
  border-radius: var(--theme-border-radius);
  box-shadow: var(--theme-box-shadow);
  padding: 1rem;
  transition: var(--theme-transition);
  border: 1px solid var(--theme-border);
}

.card:hover {
  transform: translateY(-4px);
  box-shadow: var(--theme-shadow-lg);
}

.card-header {
  background-color: var(--theme-surface-dark);
  border-bottom: 1px solid var(--theme-border);
  padding: 0.75rem 1rem;
  margin: -1rem -1rem 1rem -1rem;
  border-radius: var(--theme-border-radius) var(--theme-border-radius) 0 0;
}

/* 导航栏 */
.navbar {
  background-color: var(--theme-primary);
  color: white;
  box-shadow: var(--theme-shadow-md);
}

.navbar-brand {
  color: white !important;
  font-weight: 600;
}

.nav-link {
  color: rgba(255, 255, 255, 0.8) !important;
  transition: var(--theme-transition);
}

.nav-link:hover {
  color: white !important;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: var(--theme-border-radius);
}

/* 表格 */
.table {
  background-color: var(--theme-surface);
  color: var(--theme-text);
}

.table th {
  background-color: var(--theme-surface-dark);
  border-color: var(--theme-border);
  color: var(--theme-text);
}

.table td {
  border-color: var(--theme-border);
}

.table-striped tbody tr:nth-of-type(odd) {
  background-color: var(--theme-gray-50);
}

/* 警告框 */
.alert {
  border-radius: var(--theme-border-radius);
  border: none;
  box-shadow: var(--theme-shadow-sm);
}

.alert-success {
  background-color: var(--theme-success-light);
  color: var(--theme-success-dark);
}

.alert-info {
  background-color: var(--theme-info-light);
  color: var(--theme-info-dark);
}

.alert-warning {
  background-color: var(--theme-warning-light);
  color: var(--theme-warning-dark);
}

.alert-danger {
  background-color: var(--theme-danger-light);
  color: var(--theme-danger-dark);
}

/* 模态框 */
.modal-content {
  background-color: var(--theme-surface);
  color: var(--theme-text);
  border: 1px solid var(--theme-border);
  border-radius: var(--theme-border-radius);
  box-shadow: var(--theme-shadow-xl);
}

.modal-header {
  border-bottom: 1px solid var(--theme-border);
}

.modal-footer {
  border-top: 1px solid var(--theme-border);
}

/* 徽章 */
.badge {
  border-radius: var(--theme-border-radius);
  font-weight: 500;
}

.badge-primary {
  background-color: var(--theme-primary);
}

.badge-success {
  background-color: var(--theme-success);
}

.badge-warning {
  background-color: var(--theme-warning);
}

.badge-danger {
  background-color: var(--theme-danger);
}

/* 进度条 */
.progress {
  background-color: var(--theme-gray-200);
  border-radius: var(--theme-border-radius);
}

.progress-bar {
  background-color: var(--theme-primary);
  transition: var(--theme-transition);
}

/* 分页 */
.pagination .page-link {
  background-color: var(--theme-surface);
  border-color: var(--theme-border);
  color: var(--theme-text);
}

.pagination .page-link:hover {
  background-color: var(--theme-primary);
  border-color: var(--theme-primary);
  color: white;
}

.pagination .page-item.active .page-link {
  background-color: var(--theme-primary);
  border-color: var(--theme-primary);
}

/* 工具提示 */
.tooltip-inner {
  background-color: var(--theme-gray-800);
  color: white;
  border-radius: var(--theme-border-radius);
}

/* 下拉菜单 */
.dropdown-menu {
  background-color: var(--theme-surface);
  border: 1px solid var(--theme-border);
  border-radius: var(--theme-border-radius);
  box-shadow: var(--theme-shadow-lg);
}

.dropdown-item {
  color: var(--theme-text);
  transition: var(--theme-transition);
}

.dropdown-item:hover {
  background-color: var(--theme-primary);
  color: white;
}

/* 面包屑导航 */
.breadcrumb {
  background-color: var(--theme-surface);
  border-radius: var(--theme-border-radius);
}

.breadcrumb-item a {
  color: var(--theme-primary);
  text-decoration: none;
}

.breadcrumb-item a:hover {
  color: var(--theme-primary-dark);
}

/* 列表组 */
.list-group-item {
  background-color: var(--theme-surface);
  border-color: var(--theme-border);
  color: var(--theme-text);
}

.list-group-item:hover {
  background-color: var(--theme-surface-dark);
}

.list-group-item.active {
  background-color: var(--theme-primary);
  border-color: var(--theme-primary);
}

/* 输入组 */
.input-group-text {
  background-color: var(--theme-surface-dark);
  border-color: var(--theme-border);
  color: var(--theme-text);
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--theme-gray-100);
  border-radius: var(--theme-border-radius);
}

::-webkit-scrollbar-thumb {
  background: var(--theme-gray-400);
  border-radius: var(--theme-border-radius);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--theme-gray-500);
}

/* 深色模式滚动条 */
[data-theme="dark"] ::-webkit-scrollbar-track {
  background: var(--theme-gray-800);
}

[data-theme="dark"] ::-webkit-scrollbar-thumb {
  background: var(--theme-gray-600);
}

[data-theme="dark"] ::-webkit-scrollbar-thumb:hover {
  background: var(--theme-gray-500);
}
