/**
 * 模糊效果控制系统
 * 允许用户自定义模糊效果的启用/禁用
 */

class BlurController {
    constructor() {
        this.settings = {
            disableBackdropBlur: true,    // 禁用背景模糊
            disableTextShadow: true,      // 禁用文字阴影
            disableImageBlur: true,       // 禁用图片模糊
            disableFocusBlur: true,       // 禁用焦点模糊效果
            keepBoxShadow: true,          // 保留盒子阴影
            keepFocusEffects: true,       // 保留焦点效果（边框高亮等）
            reducedMotion: false          // 减少动画
        };

        this.init();
    }

    init() {
        this.loadSettings();
        this.applySettings();
        this.createControlPanel();
        this.bindEvents();
    }

    // 加载用户设置
    loadSettings() {
        const saved = localStorage.getItem('blurControlSettings');
        if (saved) {
            this.settings = { ...this.settings, ...JSON.parse(saved) };
        }
    }

    // 保存用户设置
    saveSettings() {
        localStorage.setItem('blurControlSettings', JSON.stringify(this.settings));
    }

    // 应用设置
    applySettings() {
        const root = document.documentElement;

        // 设置CSS变量
        root.style.setProperty('--blur-disabled', this.settings.disableBackdropBlur ? '1' : '0');
        root.style.setProperty('--keep-shadows', this.settings.keepBoxShadow ? '1' : '0');
        root.style.setProperty('--keep-focus-effects', this.settings.keepFocusEffects ? '1' : '0');
        root.style.setProperty('--disable-focus-blur', this.settings.disableFocusBlur ? '1' : '0');

        // 动态添加/移除CSS类
        document.body.classList.toggle('disable-backdrop-blur', this.settings.disableBackdropBlur);
        document.body.classList.toggle('disable-text-shadow', this.settings.disableTextShadow);
        document.body.classList.toggle('disable-image-blur', this.settings.disableImageBlur);
        document.body.classList.toggle('disable-focus-blur', this.settings.disableFocusBlur);
        document.body.classList.toggle('keep-box-shadow', this.settings.keepBoxShadow);
        document.body.classList.toggle('reduced-motion', this.settings.reducedMotion);

        console.log('模糊效果设置已应用:', this.settings);
    }

    // 创建控制面板
    createControlPanel() {
        // 检查是否已存在控制面板
        if (document.getElementById('blur-control-panel')) return;

        const panel = document.createElement('div');
        panel.id = 'blur-control-panel';
        panel.className = 'blur-control-panel';
        panel.innerHTML = `
            <div class="blur-control-header">
                <h6><i class="fas fa-eye"></i> 视觉效果设置</h6>
                <button class="btn btn-sm btn-outline-secondary" id="toggle-blur-panel">
                    <i class="fas fa-cog"></i>
                </button>
            </div>
            <div class="blur-control-content" id="blur-control-content">
                <div class="form-group">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="disable-backdrop-blur"
                               ${this.settings.disableBackdropBlur ? 'checked' : ''}>
                        <label class="form-check-label" for="disable-backdrop-blur">
                            禁用背景模糊效果
                        </label>
                    </div>
                </div>

                <div class="form-group">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="disable-text-shadow"
                               ${this.settings.disableTextShadow ? 'checked' : ''}>
                        <label class="form-check-label" for="disable-text-shadow">
                            禁用文字阴影
                        </label>
                    </div>
                </div>

                <div class="form-group">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="disable-image-blur"
                               ${this.settings.disableImageBlur ? 'checked' : ''}>
                        <label class="form-check-label" for="disable-image-blur">
                            禁用图片模糊
                        </label>
                    </div>
                </div>

                <div class="form-group">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="disable-focus-blur"
                               ${this.settings.disableFocusBlur ? 'checked' : ''}>
                        <label class="form-check-label" for="disable-focus-blur">
                            禁用焦点模糊效果
                        </label>
                    </div>
                </div>

                <div class="form-group">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="keep-box-shadow"
                               ${this.settings.keepBoxShadow ? 'checked' : ''}>
                        <label class="form-check-label" for="keep-box-shadow">
                            保留盒子阴影
                        </label>
                    </div>
                </div>

                <div class="form-group">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="keep-focus-effects"
                               ${this.settings.keepFocusEffects ? 'checked' : ''}>
                        <label class="form-check-label" for="keep-focus-effects">
                            保留焦点效果
                        </label>
                    </div>
                </div>

                <div class="form-group">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="reduced-motion"
                               ${this.settings.reducedMotion ? 'checked' : ''}>
                        <label class="form-check-label" for="reduced-motion">
                            减少动画效果
                        </label>
                    </div>
                </div>

                <div class="blur-control-actions">
                    <button class="btn btn-sm btn-primary" id="apply-blur-settings">
                        <i class="fas fa-check"></i> 应用设置
                    </button>
                    <button class="btn btn-sm btn-secondary" id="reset-blur-settings">
                        <i class="fas fa-undo"></i> 重置
                    </button>
                </div>
            </div>
        `;

        // 添加样式
        const style = document.createElement('style');
        style.textContent = `
            .blur-control-panel {
                position: fixed;
                top: 20px;
                right: 20px;
                width: 280px;
                background: var(--theme-surface, #ffffff);
                border: 1px solid var(--theme-border, #e5e7eb);
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.1);
                z-index: 9999;
                font-size: 14px;
            }

            .blur-control-header {
                padding: 12px 16px;
                border-bottom: 1px solid var(--theme-border, #e5e7eb);
                display: flex;
                justify-content: space-between;
                align-items: center;
                background: var(--theme-primary, #007bff);
                color: white;
                border-radius: 8px 8px 0 0;
            }

            .blur-control-header h6 {
                margin: 0;
                font-size: 14px;
                font-weight: 600;
            }

            .blur-control-content {
                padding: 16px;
                max-height: 400px;
                overflow-y: auto;
            }

            .blur-control-content.collapsed {
                display: none;
            }

            .blur-control-actions {
                margin-top: 16px;
                display: flex;
                gap: 8px;
            }

            .blur-control-actions .btn {
                flex: 1;
            }

            @media (max-width: 768px) {
                .blur-control-panel {
                    width: 260px;
                    right: 10px;
                    top: 10px;
                }
            }
        `;
        document.head.appendChild(style);
        document.body.appendChild(panel);
    }

    // 绑定事件
    bindEvents() {
        // 切换面板显示/隐藏
        const toggleBtn = document.getElementById('toggle-blur-panel');
        const content = document.getElementById('blur-control-content');

        if (toggleBtn && content) {
            toggleBtn.addEventListener('click', () => {
                content.classList.toggle('collapsed');
                const icon = toggleBtn.querySelector('i');
                icon.className = content.classList.contains('collapsed') ?
                    'fas fa-cog' : 'fas fa-times';
            });
        }

        // 应用设置按钮
        const applyBtn = document.getElementById('apply-blur-settings');
        if (applyBtn) {
            applyBtn.addEventListener('click', () => {
                this.updateSettingsFromForm();
                this.applySettings();
                this.saveSettings();
                this.showNotification('设置已保存并应用', 'success');
            });
        }

        // 重置设置按钮
        const resetBtn = document.getElementById('reset-blur-settings');
        if (resetBtn) {
            resetBtn.addEventListener('click', () => {
                this.resetSettings();
                this.updateFormFromSettings();
                this.applySettings();
                this.showNotification('设置已重置', 'info');
            });
        }

        // 实时预览
        const checkboxes = document.querySelectorAll('#blur-control-content input[type="checkbox"]');
        checkboxes.forEach(checkbox => {
            checkbox.addEventListener('change', () => {
                this.updateSettingsFromForm();
                this.applySettings();
            });
        });
    }

    // 从表单更新设置
    updateSettingsFromForm() {
        this.settings.disableBackdropBlur = document.getElementById('disable-backdrop-blur')?.checked || false;
        this.settings.disableTextShadow = document.getElementById('disable-text-shadow')?.checked || false;
        this.settings.disableImageBlur = document.getElementById('disable-image-blur')?.checked || false;
        this.settings.disableFocusBlur = document.getElementById('disable-focus-blur')?.checked || false;
        this.settings.keepBoxShadow = document.getElementById('keep-box-shadow')?.checked || false;
        this.settings.keepFocusEffects = document.getElementById('keep-focus-effects')?.checked || false;
        this.settings.reducedMotion = document.getElementById('reduced-motion')?.checked || false;
    }

    // 从设置更新表单
    updateFormFromSettings() {
        const elements = {
            'disable-backdrop-blur': this.settings.disableBackdropBlur,
            'disable-text-shadow': this.settings.disableTextShadow,
            'disable-image-blur': this.settings.disableImageBlur,
            'disable-focus-blur': this.settings.disableFocusBlur,
            'keep-box-shadow': this.settings.keepBoxShadow,
            'keep-focus-effects': this.settings.keepFocusEffects,
            'reduced-motion': this.settings.reducedMotion
        };

        Object.entries(elements).forEach(([id, checked]) => {
            const element = document.getElementById(id);
            if (element) element.checked = checked;
        });
    }

    // 重置设置
    resetSettings() {
        this.settings = {
            disableBackdropBlur: true,
            disableTextShadow: true,
            disableImageBlur: true,
            disableFocusBlur: true,
            keepBoxShadow: true,
            keepFocusEffects: true,
            reducedMotion: false
        };
    }

    // 显示通知
    showNotification(message, type = 'info') {
        // 如果有toastr，使用toastr
        if (window.toastr) {
            window.toastr[type](message);
            return;
        }

        // 否则使用简单的通知
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} alert-dismissible fade show`;
        notification.style.cssText = `
            position: fixed;
            top: 80px;
            right: 20px;
            z-index: 10000;
            min-width: 250px;
        `;
        notification.innerHTML = `
            ${message}
            <button type="button" class="close" data-dismiss="alert">
                <span>&times;</span>
            </button>
        `;

        document.body.appendChild(notification);

        // 3秒后自动移除
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 3000);
    }

    // 公共方法：快速禁用所有模糊效果
    disableAllBlur() {
        this.settings = {
            disableBackdropBlur: true,
            disableTextShadow: true,
            disableImageBlur: true,
            disableFocusBlur: true,
            keepBoxShadow: false,
            keepFocusEffects: false,
            reducedMotion: true
        };
        this.applySettings();
        this.saveSettings();
        this.updateFormFromSettings();
    }

    // 公共方法：启用所有效果
    enableAllEffects() {
        this.settings = {
            disableBackdropBlur: false,
            disableTextShadow: false,
            disableImageBlur: false,
            disableFocusBlur: false,
            keepBoxShadow: true,
            keepFocusEffects: true,
            reducedMotion: false
        };
        this.applySettings();
        this.saveSettings();
        this.updateFormFromSettings();
    }
}

// 初始化模糊控制器
document.addEventListener('DOMContentLoaded', function() {
    window.blurController = new BlurController();

    // 添加全局快捷键
    document.addEventListener('keydown', function(e) {
        // Ctrl+Shift+B 快速切换模糊效果
        if (e.ctrlKey && e.shiftKey && e.key === 'B') {
            e.preventDefault();
            const panel = document.getElementById('blur-control-panel');
            if (panel) {
                panel.style.display = panel.style.display === 'none' ? 'block' : 'none';
            }
        }
    });
});

// 导出给其他脚本使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = BlurController;
}
