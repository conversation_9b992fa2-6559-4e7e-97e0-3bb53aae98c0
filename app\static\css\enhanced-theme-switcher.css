/* 增强主题切换器样式 */

/* 主题切换器容器 */
.theme-switcher {
    position: relative;
}

/* 主题切换器按钮 */
.theme-switcher .btn {
    border-radius: 20px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.theme-switcher .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

/* 下拉菜单样式 */
.theme-switcher .dropdown-menu {
    border: none;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
    padding: 8px;
    background: var(--theme-surface, #ffffff);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

/* 主题分类区域 */
.theme-category-section {
    margin-bottom: 8px;
}

.theme-category-section:last-child {
    margin-bottom: 0;
}

/* 分类标题 */
.theme-category-section .dropdown-header {
    font-size: 12px;
    font-weight: 600;
    color: var(--theme-text-secondary, #6b7280);
    padding: 8px 12px 4px;
    margin-bottom: 4px;
    border-bottom: 1px solid var(--theme-border, #e5e7eb);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.category-icon {
    font-size: 14px;
    margin-right: 6px;
}

/* 主题选项 */
.theme-option {
    padding: 8px 12px !important;
    border-radius: 8px;
    margin-bottom: 2px;
    transition: all 0.2s ease;
    border: 2px solid transparent;
    text-decoration: none !important;
}

.theme-option:hover {
    background-color: var(--theme-primary-50, #eff6ff) !important;
    transform: translateX(2px);
}

.theme-option.active {
    background-color: var(--theme-primary-100, #dbeafe) !important;
    border-color: var(--theme-primary, #2563eb);
    box-shadow: 0 2px 4px rgba(37, 99, 235, 0.1);
}

/* 主题预览容器 */
.theme-preview-container {
    flex-shrink: 0;
}

.theme-preview-box {
    width: 32px;
    height: 24px;
    border-radius: 6px;
    position: relative;
    border: 2px solid rgba(255,255,255,0.2);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    overflow: hidden;
}

.theme-accent-dot {
    position: absolute;
    top: 2px;
    right: 2px;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    border: 1px solid rgba(255,255,255,0.3);
}

/* 主题信息 */
.theme-info {
    min-width: 0;
}

.theme-name {
    font-size: 14px;
    font-weight: 500;
    color: var(--theme-text, #111827);
    margin-bottom: 2px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.theme-description {
    font-size: 11px;
    color: var(--theme-text-muted, #9ca3af);
    line-height: 1.2;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 活跃状态指示器 */
.theme-option .fas.fa-check {
    font-size: 12px;
    opacity: 0.8;
}

/* 下拉菜单头部 */
.dropdown-header {
    font-size: 13px;
    font-weight: 600;
    color: var(--theme-text, #111827);
    padding: 12px 16px 8px;
    border-bottom: 1px solid var(--theme-border, #e5e7eb);
    margin-bottom: 8px;
}

/* 下拉菜单底部 */
.dropdown-item-text {
    padding: 8px 16px;
    text-align: center;
}

.dropdown-item-text small {
    color: var(--theme-text-muted, #9ca3af);
    font-size: 11px;
}

/* 分割线 */
.dropdown-divider {
    border-color: var(--theme-border, #e5e7eb);
    margin: 8px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .theme-switcher .dropdown-menu {
        min-width: 280px;
        max-height: 400px;
    }
    
    .theme-preview-box {
        width: 28px;
        height: 20px;
    }
    
    .theme-accent-dot {
        width: 6px;
        height: 6px;
    }
    
    .theme-name {
        font-size: 13px;
    }
    
    .theme-description {
        font-size: 10px;
    }
}

/* 深色模式适配 */
[data-theme="dark"] .theme-switcher .dropdown-menu {
    background: var(--theme-surface, #1e293b);
    border: 1px solid var(--theme-border, #334155);
}

[data-theme="dark"] .theme-option:hover {
    background-color: var(--theme-surface-dark, #0f172a) !important;
}

[data-theme="dark"] .theme-option.active {
    background-color: var(--theme-primary-800, #1e40af) !important;
    border-color: var(--theme-primary, #4f46e5);
}

[data-theme="dark"] .theme-name {
    color: var(--theme-text, #ecedee);
}

[data-theme="dark"] .theme-description {
    color: var(--theme-text-secondary, #94a3b8);
}

[data-theme="dark"] .dropdown-header {
    color: var(--theme-text, #ecedee);
    border-color: var(--theme-border, #334155);
}

[data-theme="dark"] .dropdown-divider {
    border-color: var(--theme-border, #334155);
}

/* 现代科技主题特殊效果 */
[data-theme="modern"] .theme-switcher .dropdown-menu {
    background: var(--theme-surface, #1a1a2e);
    border: 1px solid var(--theme-accent, #00f5d4);
    box-shadow: 0 10px 30px rgba(0, 245, 212, 0.1);
}

[data-theme="modern"] .theme-option:hover {
    background-color: rgba(0, 245, 212, 0.1) !important;
    box-shadow: 0 0 10px rgba(0, 245, 212, 0.2);
}

[data-theme="modern"] .theme-option.active {
    background-color: rgba(0, 245, 212, 0.2) !important;
    border-color: var(--theme-accent, #00f5d4);
    box-shadow: 0 0 15px rgba(0, 245, 212, 0.3);
}

/* 动画效果 */
@keyframes themeSwitch {
    0% {
        opacity: 0;
        transform: scale(0.95);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

.theme-switcher .dropdown-menu.show {
    animation: themeSwitch 0.2s ease-out;
}

/* 主题预览悬停效果 */
.theme-preview-box {
    transition: all 0.2s ease;
}

.theme-option:hover .theme-preview-box {
    transform: scale(1.05);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.theme-option.active .theme-preview-box {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
}

/* 主题切换按钮特殊状态 */
.theme-switcher .btn.btn-outline-secondary {
    border-color: var(--theme-border, #e5e7eb);
    color: var(--theme-text-secondary, #6b7280);
}

.theme-switcher .btn.btn-outline-secondary:hover {
    background-color: var(--theme-primary, #2563eb);
    border-color: var(--theme-primary, #2563eb);
    color: white;
}

/* 滚动条样式 */
.theme-switcher .dropdown-menu::-webkit-scrollbar {
    width: 4px;
}

.theme-switcher .dropdown-menu::-webkit-scrollbar-track {
    background: var(--theme-gray-100, #f3f4f6);
    border-radius: 2px;
}

.theme-switcher .dropdown-menu::-webkit-scrollbar-thumb {
    background: var(--theme-gray-400, #9ca3af);
    border-radius: 2px;
}

.theme-switcher .dropdown-menu::-webkit-scrollbar-thumb:hover {
    background: var(--theme-gray-500, #6b7280);
}

/* 深色模式滚动条 */
[data-theme="dark"] .theme-switcher .dropdown-menu::-webkit-scrollbar-track {
    background: var(--theme-gray-800, #1f2937);
}

[data-theme="dark"] .theme-switcher .dropdown-menu::-webkit-scrollbar-thumb {
    background: var(--theme-gray-600, #4b5563);
}

[data-theme="dark"] .theme-switcher .dropdown-menu::-webkit-scrollbar-thumb:hover {
    background: var(--theme-gray-500, #6b7280);
}
