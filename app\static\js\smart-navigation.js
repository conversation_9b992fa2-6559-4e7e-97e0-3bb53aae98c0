/**
 * 智能导航栏 - 根据屏幕大小和用户偏好自动优化菜单显示
 * 解决导航菜单过多的问题
 */

class SmartNavigation {
    constructor() {
        this.settings = {
            mode: 'auto',              // auto, desktop, mobile, minimal
            showIcons: true,           // 是否显示图标
            groupSimilar: true,        // 是否合并相似功能
            maxMainMenus: 5,           // 最大主菜单数量
            collapseThreshold: 768,    // 折叠阈值（px）
            autoHideUnused: true       // 自动隐藏未使用的菜单
        };
        
        this.menuData = null;
        this.currentMode = 'desktop';
        this.init();
    }

    init() {
        this.loadSettings();
        this.detectScreenSize();
        this.createNavigationControls();
        this.bindEvents();
        this.optimizeNavigation();
    }

    // 检测屏幕大小
    detectScreenSize() {
        const width = window.innerWidth;
        
        if (this.settings.mode === 'auto') {
            if (width < this.settings.collapseThreshold) {
                this.currentMode = 'mobile';
            } else if (width < 1200) {
                this.currentMode = 'compact';
            } else {
                this.currentMode = 'desktop';
            }
        } else {
            this.currentMode = this.settings.mode;
        }
        
        console.log(`导航模式: ${this.currentMode} (屏幕宽度: ${width}px)`);
    }

    // 创建导航控制器
    createNavigationControls() {
        // 在统一控制中心添加导航优化选项
        const controlCenter = document.querySelector('.control-center-menu');
        if (!controlCenter) return;

        // 查找合适的位置插入导航控制
        const lastSection = controlCenter.querySelector('.control-section:last-of-type');
        if (!lastSection) return;

        const navigationSection = document.createElement('div');
        navigationSection.className = 'control-section';
        navigationSection.innerHTML = `
            <div class="section-title">
                <i class="fas fa-bars"></i> 导航优化
            </div>
            <div class="navigation-controls">
                <div class="control-row">
                    <label>显示模式</label>
                    <select class="form-control form-control-sm" id="nav-mode-select">
                        <option value="auto" selected>自动适配</option>
                        <option value="desktop">桌面模式</option>
                        <option value="compact">紧凑模式</option>
                        <option value="mobile">移动模式</option>
                        <option value="minimal">极简模式</option>
                    </select>
                </div>
                <div class="setting-toggles">
                    <label class="toggle-item">
                        <input type="checkbox" id="nav-show-icons" checked>
                        <span class="toggle-slider"></span>
                        <span class="toggle-label">显示图标</span>
                    </label>
                    <label class="toggle-item">
                        <input type="checkbox" id="nav-group-similar" checked>
                        <span class="toggle-slider"></span>
                        <span class="toggle-label">合并相似功能</span>
                    </label>
                    <label class="toggle-item">
                        <input type="checkbox" id="nav-auto-hide" checked>
                        <span class="toggle-slider"></span>
                        <span class="toggle-label">隐藏未使用菜单</span>
                    </label>
                </div>
                <div class="navigation-stats" id="nav-stats">
                    <small class="text-muted">
                        <i class="fas fa-info-circle"></i> 
                        当前显示: <span id="visible-menus">5</span> 个主菜单
                    </small>
                </div>
            </div>
        `;

        // 插入到最后一个section之前
        lastSection.parentNode.insertBefore(navigationSection, lastSection);
    }

    // 绑定事件
    bindEvents() {
        // 窗口大小变化
        window.addEventListener('resize', () => {
            this.detectScreenSize();
            this.optimizeNavigation();
        });

        // 导航模式切换
        const modeSelect = document.getElementById('nav-mode-select');
        if (modeSelect) {
            modeSelect.addEventListener('change', (e) => {
                this.settings.mode = e.target.value;
                this.detectScreenSize();
                this.optimizeNavigation();
                this.saveSettings();
            });
        }

        // 设置开关
        const toggles = ['nav-show-icons', 'nav-group-similar', 'nav-auto-hide'];
        toggles.forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.addEventListener('change', () => {
                    this.updateSettings();
                    this.optimizeNavigation();
                });
            }
        });

        // 菜单使用统计
        document.addEventListener('click', (e) => {
            const menuLink = e.target.closest('.nav-link, .dropdown-item');
            if (menuLink) {
                this.trackMenuUsage(menuLink);
            }
        });
    }

    // 优化导航栏
    optimizeNavigation() {
        const navbar = document.querySelector('.navbar-nav');
        if (!navbar) return;

        // 获取所有菜单项（排除控制中心）
        const menuItems = Array.from(navbar.children).filter(item => 
            !item.querySelector('#controlCenterDropdown')
        );

        switch (this.currentMode) {
            case 'mobile':
                this.applyMobileMode(menuItems);
                break;
            case 'compact':
                this.applyCompactMode(menuItems);
                break;
            case 'minimal':
                this.applyMinimalMode(menuItems);
                break;
            default:
                this.applyDesktopMode(menuItems);
        }

        this.updateNavigationStats();
    }

    // 应用移动模式
    applyMobileMode(menuItems) {
        // 隐藏大部分菜单，只保留最重要的
        const importantMenus = ['dashboard', 'daily_management', 'menu_planning'];
        
        menuItems.forEach(item => {
            const menuId = this.getMenuId(item);
            if (importantMenus.includes(menuId)) {
                item.style.display = 'block';
                this.simplifyMenuItem(item);
            } else {
                item.style.display = 'none';
            }
        });

        // 创建"更多"菜单
        this.createMoreMenu(menuItems.filter(item => 
            !importantMenus.includes(this.getMenuId(item))
        ));
    }

    // 应用紧凑模式
    applyCompactMode(menuItems) {
        // 合并相似功能
        if (this.settings.groupSimilar) {
            this.groupSimilarMenus(menuItems);
        }

        // 隐藏图标（如果设置）
        if (!this.settings.showIcons) {
            this.hideMenuIcons(menuItems);
        }

        // 限制主菜单数量
        if (menuItems.length > this.settings.maxMainMenus) {
            this.collapseExcessMenus(menuItems);
        }
    }

    // 应用极简模式
    applyMinimalMode(menuItems) {
        // 只显示3个最重要的菜单
        const essentialMenus = ['dashboard', 'daily_management', 'menu_planning'];
        
        menuItems.forEach(item => {
            const menuId = this.getMenuId(item);
            if (essentialMenus.includes(menuId)) {
                item.style.display = 'block';
                this.simplifyMenuItem(item);
            } else {
                item.style.display = 'none';
            }
        });

        // 创建功能中心菜单
        this.createFunctionCenter(menuItems.filter(item => 
            !essentialMenus.includes(this.getMenuId(item))
        ));
    }

    // 应用桌面模式
    applyDesktopMode(menuItems) {
        // 显示所有菜单
        menuItems.forEach(item => {
            item.style.display = 'block';
        });

        // 根据设置调整显示
        if (!this.settings.showIcons) {
            this.hideMenuIcons(menuItems);
        }

        if (this.settings.autoHideUnused) {
            this.hideUnusedMenus(menuItems);
        }
    }

    // 创建"更多"菜单
    createMoreMenu(hiddenItems) {
        if (hiddenItems.length === 0) return;

        const navbar = document.querySelector('.navbar-nav');
        const existingMore = navbar.querySelector('#more-menu');
        if (existingMore) {
            existingMore.remove();
        }

        const moreMenu = document.createElement('li');
        moreMenu.className = 'nav-item dropdown';
        moreMenu.id = 'more-menu';
        moreMenu.innerHTML = `
            <a class="nav-link dropdown-toggle" href="#" role="button" data-toggle="dropdown">
                <i class="fas fa-ellipsis-h"></i> 更多
            </a>
            <div class="dropdown-menu dropdown-menu-right">
                ${hiddenItems.map(item => {
                    const link = item.querySelector('.nav-link');
                    const icon = link.querySelector('i');
                    const text = link.textContent.trim();
                    const href = link.getAttribute('href');
                    
                    return `
                        <a class="dropdown-item" href="${href}">
                            ${icon ? icon.outerHTML : ''} ${text}
                        </a>
                    `;
                }).join('')}
            </div>
        `;

        // 插入到控制中心之前
        const controlCenter = navbar.querySelector('li:last-child');
        navbar.insertBefore(moreMenu, controlCenter);
    }

    // 创建功能中心
    createFunctionCenter(hiddenItems) {
        if (hiddenItems.length === 0) return;

        const navbar = document.querySelector('.navbar-nav');
        const existingCenter = navbar.querySelector('#function-center');
        if (existingCenter) {
            existingCenter.remove();
        }

        const functionCenter = document.createElement('li');
        functionCenter.className = 'nav-item dropdown';
        functionCenter.id = 'function-center';
        functionCenter.innerHTML = `
            <a class="nav-link dropdown-toggle" href="#" role="button" data-toggle="dropdown">
                <i class="fas fa-th"></i> 功能
            </a>
            <div class="dropdown-menu dropdown-menu-right function-center-menu">
                <div class="function-grid">
                    ${hiddenItems.map(item => {
                        const link = item.querySelector('.nav-link');
                        const icon = link.querySelector('i');
                        const text = link.textContent.trim();
                        const href = link.getAttribute('href');
                        
                        return `
                            <a class="function-card" href="${href}">
                                <div class="function-icon">
                                    ${icon ? icon.outerHTML : '<i class="fas fa-circle"></i>'}
                                </div>
                                <div class="function-name">${text}</div>
                            </a>
                        `;
                    }).join('')}
                </div>
            </div>
        `;

        // 添加功能中心样式
        this.addFunctionCenterStyles();

        // 插入到控制中心之前
        const controlCenter = navbar.querySelector('li:last-child');
        navbar.insertBefore(functionCenter, controlCenter);
    }

    // 添加功能中心样式
    addFunctionCenterStyles() {
        if (document.getElementById('function-center-styles')) return;

        const style = document.createElement('style');
        style.id = 'function-center-styles';
        style.textContent = `
            .function-center-menu {
                min-width: 300px;
                padding: 16px;
            }
            
            .function-grid {
                display: grid;
                grid-template-columns: repeat(3, 1fr);
                gap: 12px;
            }
            
            .function-card {
                display: flex;
                flex-direction: column;
                align-items: center;
                padding: 12px 8px;
                border-radius: 8px;
                text-decoration: none;
                color: var(--theme-text, #333);
                transition: all 0.2s ease;
                border: 1px solid var(--theme-border, #e5e7eb);
            }
            
            .function-card:hover {
                background: var(--theme-primary-50, #eff6ff);
                border-color: var(--theme-primary, #007bff);
                text-decoration: none;
                color: var(--theme-primary, #007bff);
                transform: translateY(-1px);
            }
            
            .function-icon {
                font-size: 20px;
                margin-bottom: 6px;
            }
            
            .function-name {
                font-size: 11px;
                text-align: center;
                font-weight: 500;
            }
            
            @media (max-width: 768px) {
                .function-grid {
                    grid-template-columns: repeat(2, 1fr);
                }
                
                .function-center-menu {
                    min-width: 250px;
                }
            }
        `;
        document.head.appendChild(style);
    }

    // 简化菜单项
    simplifyMenuItem(item) {
        const link = item.querySelector('.nav-link');
        if (!link) return;

        // 移除图标（如果设置）
        if (!this.settings.showIcons) {
            const icon = link.querySelector('i');
            if (icon) icon.remove();
        }

        // 简化文字
        const text = link.textContent.trim();
        const shortText = this.getShortText(text);
        if (shortText !== text) {
            link.innerHTML = link.innerHTML.replace(text, shortText);
        }
    }

    // 获取简化文字
    getShortText(text) {
        const shortMap = {
            '食堂日常管理': '日常',
            '周菜单管理': '菜单',
            '采购管理': '采购',
            '库存管理': '库存',
            '供应商管理': '供应商',
            '食材管理': '食材',
            '食品安全': '安全',
            '报表统计': '报表',
            '系统管理': '系统'
        };
        return shortMap[text] || text;
    }

    // 获取菜单ID
    getMenuId(item) {
        const link = item.querySelector('.nav-link');
        if (!link) return '';
        
        const dropdown = link.getAttribute('id');
        if (dropdown) {
            return dropdown.replace('Dropdown', '');
        }
        
        return link.textContent.trim().toLowerCase();
    }

    // 跟踪菜单使用情况
    trackMenuUsage(menuLink) {
        const menuText = menuLink.textContent.trim();
        const usage = JSON.parse(localStorage.getItem('menuUsage') || '{}');
        usage[menuText] = (usage[menuText] || 0) + 1;
        localStorage.setItem('menuUsage', JSON.stringify(usage));
    }

    // 隐藏未使用的菜单
    hideUnusedMenus(menuItems) {
        const usage = JSON.parse(localStorage.getItem('menuUsage') || '{}');
        const threshold = 5; // 使用次数阈值

        menuItems.forEach(item => {
            const link = item.querySelector('.nav-link');
            const menuText = link.textContent.trim();
            
            if (usage[menuText] < threshold) {
                item.style.opacity = '0.6';
                item.title = `使用次数: ${usage[menuText] || 0}`;
            }
        });
    }

    // 更新导航统计
    updateNavigationStats() {
        const navbar = document.querySelector('.navbar-nav');
        const visibleMenus = Array.from(navbar.children).filter(item => 
            item.style.display !== 'none' && !item.querySelector('#controlCenterDropdown')
        ).length;

        const statsElement = document.getElementById('visible-menus');
        if (statsElement) {
            statsElement.textContent = visibleMenus;
        }
    }

    // 更新设置
    updateSettings() {
        this.settings.showIcons = document.getElementById('nav-show-icons')?.checked || true;
        this.settings.groupSimilar = document.getElementById('nav-group-similar')?.checked || true;
        this.settings.autoHideUnused = document.getElementById('nav-auto-hide')?.checked || true;
        this.saveSettings();
    }

    // 加载设置
    loadSettings() {
        const saved = localStorage.getItem('smartNavigationSettings');
        if (saved) {
            this.settings = { ...this.settings, ...JSON.parse(saved) };
        }
    }

    // 保存设置
    saveSettings() {
        localStorage.setItem('smartNavigationSettings', JSON.stringify(this.settings));
    }
}

// 初始化智能导航
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(() => {
        window.smartNavigation = new SmartNavigation();
        console.log('🧭 智能导航系统已初始化');
    }, 1000);
});

// 导出给其他脚本使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SmartNavigation;
}
